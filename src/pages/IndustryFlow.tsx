import React, { useState, useCallback, useRef, useEffect } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { useTheme } from '@/components/ThemeProvider';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { ArrowLeft, Plus, Circle, Square, FolderPlus, Link2, Pencil, CircleX, Trash } from 'lucide-react';
import {
  ReactFlow,
  MiniMap,
  Controls,
  Background,
  useNodesState,
  useEdgesState,
  addEdge,
  MarkerType,
  Panel,
  Handle,
  Position,
  Connection,
  OnConnectStartParams,
  NodeChange,
  EdgeChange,
} from '@xyflow/react';
import '@xyflow/react/dist/style.css';
import { FlowModeDialog, FlowModeType } from "@/components/FlowModeDialog";
import { ConnectionFormDialog } from "@/components/ConnectionFormDialog";
import { CustomNode, FinalOutputNode, InputNode } from '../components/IndustryFlowNodes';
import {
  initialNodes,
  initialEdges,
  inputNodes,
  virtualInputNodes,
  edgeLabelStyle,
  edgeStyle,
  flowTypes,
  nodeDefaultStyle,
  VERTICAL_GAP,
} from './IndustryFlow.constants';
import { defaultNodeFormDataMap } from './IndustryFlow.defaultFormData';
import { useToastContext } from "@/contexts/ToastContext";
import { horizontalGridAutoLayout } from '@/utils/autoLayout';
import { IndustryFlowHeader } from '../components/IndustryFlowHeader';
import { IndustryFlowNodeActions } from '../components/IndustryFlowNodeActions';
import { EditNodeDialog, EditEdgeDialog } from '../components/IndustryFlowDialogs';
import { DualTabNodeEditor } from '../components/DualTabNodeEditor';
import { ScenarioComparison } from '../components/ScenarioComparison';
import { ResultsBoard } from '../components/ResultsBoard';
import { processConnectionsFromFormData } from '@/utils/connectionUtils';
import { ScenarioSidebar } from '../components/ScenarioSidebar';
import { AddConstraintsModal } from '../components/AddConstraintsModal';
import { NodeCreationDialog } from '../components/NodeCreationDialog';
import OptimizerSidebar from '../components/OptimizerSidebar';
import { getActivitySlug } from '@/utils/common';

// API imports
import { createFlowDiagram, fetchFlowDiagrams, FlowDiagramResponse, saveOrUpdateFlowDiagram } from '@/services/flowDiagramApi';
import { getSectorUuidFromIndustryId } from '@/services/activitiesApi';
import { useSectors } from '@/hooks/useSectors';
import { convertDefaultFlowToFormData, convertDefaultFlowToInitialNodes } from '@/utils/defaultFlowConverter';
import { OverwriteConfirmDialog } from '@/components/OverwriteConfirmDialog';



const nodeTypes = {
  custom: CustomNode,
  finalOutput: FinalOutputNode,
  input: InputNode,
};

// Type definition for saved flows - update SavedFlow interface
interface SavedFlow {
  title: string;
  nodes: any[];
  edges: any[];
  flowType?: 'inventory' | 'scenario';
  nodeFormData?: Record<string, any>; // Include form data for proper restoration
}

// Ultra-optimized industry flow schema based on your reference
export interface UltraOptimizedFlowSchema {
  nodes: Record<string, UltraOptimizedNode>;
  metadata: {
    createdAt: string;
    updatedAt: string;
  };
}

// Ultra-optimized node structure
export interface UltraOptimizedNode {
  activity: string;
  position: { x: number; y: number };
  technologies: UltraOptimizedTechnology[];
}

// Ultra-optimized technology structure
export interface UltraOptimizedTechnology {
  name: string;
  isCustom?: boolean;
  startYear: number;
  endYear: number;
  emission?: number; // Technology emission value
  inputs: {
    materials: UltraOptimizedMaterialInput[];
    energies: UltraOptimizedEnergyInput[];
    emissions: UltraOptimizedEmissionInput[];
  };
  outputs: {
    materials: UltraOptimizedMaterialOutput[];
    energies: UltraOptimizedEnergyOutput[];
  };
  byproducts: {
    materials: UltraOptimizedMaterialByproduct[];
    energies: UltraOptimizedEnergyByproduct[];
  };
  financial: UltraOptimizedFinancial;
}

// Ultra-optimized input structures
export interface UltraOptimizedMaterialInput {
  id: string;
  material: string;
  unit: string;
  quantity: number;
  cost: number;
  specificMaterialCost: number;
  sourceNodeId?: string; // null/undefined for standalone inputs
  sourceTechnology?: string;
  lowerBound?: number; // Lower bound percentage
  upperBound?: number; // Upper bound percentage
}

export interface UltraOptimizedEnergyInput {
  id: string;
  energy: string;
  unit: string;
  quantity: number;
  cost: number;
  specificEnergyCost: number;
  sourceNodeId?: string;
  sourceTechnology?: string;
  lowerBound?: number; // Lower bound percentage
  upperBound?: number; // Upper bound percentage
}

export interface UltraOptimizedEmissionInput {
  id: string;
  emission: string;
  unit: string;
  emissionFactor: number;
  cost: number;
}

// Ultra-optimized output structures
export interface UltraOptimizedMaterialOutput {
  id: string;
  material: string;
  unit: string;
  quantity: number;
  specificMaterialCost: number; // Add missing SMC field
  isFinalOutput: boolean;
  targetNodeId: string | null;
  targetTechnology: string | null;
}

export interface UltraOptimizedEnergyOutput {
  id: string;
  energy: string;
  unit: string;
  quantity: number;
  specificEnergyCost: number; // Add missing SEC field
  isFinalOutput: boolean;
  targetNodeId: string | null;
  targetTechnology: string | null;
}

// Ultra-optimized byproduct structures
export interface UltraOptimizedMaterialByproduct {
  id: string;
  material: string;
  unit: string;
  quantity: number;
  specificMaterialCost: number; // Add SMC field for consistency
  isFinalOutput: boolean;
  targetNodeId: string | null;
  targetTechnology: string | null;
}

export interface UltraOptimizedEnergyByproduct {
  id: string;
  energy: string;
  unit: string;
  quantity: number;
  specificEnergyCost: number; // Add SEC field for consistency
  isFinalOutput: boolean;
  targetNodeId: string | null;
  targetTechnology: string | null;
}

// Ultra-optimized financial structure
export interface UltraOptimizedFinancial {
  capacity: number;
  capacityUnit: string;
  capitalCost: number;
  capitalCostUnit: string;
  operatingMaintenanceCost: number;
  operatingMaintenanceCostUnit: string;
}

// Legacy schema for backward compatibility
export interface IndustryFlowSchema {
  id?: string;
  name: string;
  flowType: 'inventory' | 'scenario';
  createdAt: string;
  updatedAt: string;
  // Essential node data with form data containing coordinates
  nodeData: Record<string, NodeDataWithFormData>;
  // Meta information
  sectorUuid?: string;
  industryId?: string;
}

// Node data with embedded coordinates and complete form data
export interface NodeDataWithFormData {
  // Basic node info
  id: string;
  label: string;
  type: string;
  // Position stored with node data
  position: { x: number; y: number };
  // Complete 4-tab form data
  formData: {
    // Basic node info
    activity: string;
    currentTechnology: string; // Currently selected technology
    customTechnology?: string;
    customActivity?: string;

    // Inputs tab (generates standalone input edges)
    materialInputs: MaterialInput[];
    energyInputs: EnergyInput[];
    emissions: EmissionInput[];

    // Outputs tab (generates output edges and final output components)
    materialOutputs: MaterialOutput[];
    energyOutputs: EnergyOutput[];

    // By-products tab (generates byproduct edges)
    materialByProducts: MaterialByProduct[];
    energyByProducts: EnergyByProduct[];

    // Financial tab
    financial: FinancialData;
    financialEntries: Record<string, any>;

    // Legacy fields for compatibility
    energyInput?: any;
    emission?: any;
    materialInput?: any;
    byproductTechnology?: string;
    byproductEnergy?: any;
    byproductMaterial?: any;
  };
  // Technology data with embedded form data for each technology
  technologies: TechnologyData[];
  completedAt: string;
}

// Supporting interfaces for form data with full names
export interface MaterialInput {
  id: string;
  material: string;
  unit: string;
  cost: string;
  specificMaterialCost: string; // Full name instead of 'smc'
  sourceActivity: string;
  technology: string;
}

export interface EnergyInput {
  id: string;
  energy: string;
  unit: string;
  cost: string;
  specificEnergyCost: string; // Full name instead of 'sec'
  sourceActivity: string;
  technology: string;
}

export interface EmissionInput {
  id: string;
  emission: string;
  emissionFactor: string; // Full name instead of 'ef'
  unit: string;
  sourceActivity: string;
  technology: string;
}

export interface MaterialOutput {
  id: string;
  material: string;
  unit: string;
  specificMaterialCost: string;
  isFinalOutput: boolean;
  connectToNode: string;
  quantity: string;
  quantityUnit: string;
  destinationTechnology?: string;
}

export interface EnergyOutput {
  id: string;
  energy: string;
  unit: string;
  specificEnergyCost: string;
  isFinalOutput: boolean;
  connectToNode: string;
  quantity: string;
  quantityUnit: string;
  destinationTechnology?: string;
}

export interface MaterialByProduct {
  id: string;
  byproduct: string;
  unit: string;
  byproductPerPrimaryOutput: string; // Full name instead of 'bppo'
  connectToNode: string;
  replacedMaterial: string; // More descriptive than 'replaced'
  technologyEmissionFactor?: string;
  emissionFactor?: string;
  emissionUnit?: string;
}

export interface EnergyByProduct {
  id: string;
  byproduct: string;
  unit: string;
  byproductPerPrimaryOutput: string;
  connectToNode: string;
  replacedEnergy: string;
}

export interface FinancialData {
  capacity: string;
  capacityUnit: string;
  capitalCost: string; // Renamed from capitalCostUnit for clarity
  operatingMaintenanceCost: string; // Renamed from operatingAndMaintenanceCost for consistency
}

// Legacy technology data structure for backward compatibility
export interface TechnologyData {
  name: string;
  formData: {
    activity: string;
    technology: string;
    parameters?: Record<string, any>;
    materialInputs?: MaterialInput[];
    energyInputs?: EnergyInput[];
    emissions?: EmissionInput[];
    materialOutputs?: MaterialOutput[];
    energyOutputs?: EnergyOutput[];
    materialByProducts?: MaterialByProduct[];
    energyByProducts?: EnergyByProduct[];
    financial?: FinancialData;
  };
}

// Interface for node data
interface NodeData {
  label: string;
  subflowParent?: string;
  technology?: string;
  outputs?: {
    energy?: string;
    material?: string;
    energyUnit?: string;
    materialUnit?: string;
    energySEC?: string;
    materialSMC?: string;
  };
}

function getSectorNameFromParam(industryId) {
  if (!industryId) return "Industry";
  return industryId
    .replace(/[-_]/g, ' ')
    .replace(/\b\w/g, (c) => c.toUpperCase());
}

function getFlowTitleFromLocation(industryId, location, entryNode, flowType) {
  const searchParams = new URLSearchParams(location.search);
  if (searchParams.has('sector') && (!entryNode || !flowType)) {
    return searchParams.get('sector');
  }
  if (entryNode && flowType) {
    const nodeLabel = entryNode.data?.label || `Node`;
    if (flowType === "subflow") {
      return `Subflow of ${nodeLabel}`;
    }
    if (flowType === "byproduct") {
      return `By-product Flow of ${nodeLabel}`;
    }
  }
  return getSectorNameFromParam(industryId);
}

const IndustryFlow = () => {
  const { theme } = useTheme();
  const { industryId } = useParams<{ industryId: string }>();
  const location = useLocation();
  const navigate = useNavigate();
  const { toast } = useToastContext();
  const { sectors } = useSectors();

  const locationState = (location as any).state || {};
  const entryNodeFromState = locationState.entryNode;
  const flowTypeFromState = locationState.flowType;

  const [showModeDialog, setShowModeDialog] = useState(true);
  const [flowMode, setFlowMode] = useState<"base" | "scratch" | null>(null);
  const [showNodeCreationDialog, setShowNodeCreationDialog] = useState(false);

  const sectorName = getFlowTitleFromLocation(
    industryId,
    location,
    entryNodeFromState,
    flowTypeFromState
  );

  const reactFlowRef = useRef<any>(null);

  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  
  // Store original nodes and edges when entering subflow mode
  const [originalNodes, setOriginalNodes] = useState<any[]>([]);
  const [originalEdges, setOriginalEdges] = useState<any[]>([]);

  // New state for scenario mode
  const [isScenarioMode, setIsScenarioMode] = useState(false);
  const [inventoryNodes, setInventoryNodes] = useState<any[]>([]);
  const [inventoryEdges, setInventoryEdges] = useState<any[]>([]);
  
  // State for saved flows (local storage - deprecated)
  const [savedFlows, setSavedFlows] = useState<SavedFlow[]>([]);

  // State for API-based flow diagrams
  const [flowDiagrams, setFlowDiagrams] = useState<FlowDiagramResponse[]>([]);
  const [isLoadingFlowDiagrams, setIsLoadingFlowDiagrams] = useState(false);
  const [currentFlowDiagramUuid, setCurrentFlowDiagramUuid] = useState<string | null>(null);
  
  // State for subflow mode
  const [isSubflowMode, setIsSubflowMode] = useState(false);
  const [activeNodeId, setActiveNodeId] = useState<string | null>(null);
  const [subflowType, setSubflowType] = useState<"subflow" | "byproduct" | null>(null);

  // New state to track connection in progress
  const [connectionStartParams, setConnectionStartParams] = useState<OnConnectStartParams | null>(null);
  const [connectionEndNode, setConnectionEndNode] = useState<string | null>(null);

  // Load saved flows from localStorage on component mount (deprecated)
  React.useEffect(() => {
    try {
      const savedFlowsData = localStorage.getItem('savedFlows');
      if (savedFlowsData) {
        setSavedFlows(JSON.parse(savedFlowsData));
      }
    } catch (error) {
      console.error('Error loading saved flows:', error);
    }
  }, []);

  // Load flow diagrams from API on component mount
  React.useEffect(() => {
    const loadFlowDiagrams = async () => {
      setIsLoadingFlowDiagrams(true);
      try {
        // Get sector UUID from industry ID
        const sectorUuid = await getSectorUuidFromIndustryId(industryId || '');

        // Fetch flow diagrams for this sector
        const diagrams = await fetchFlowDiagrams(sectorUuid, { toast });
        setFlowDiagrams(diagrams);

        console.log('=== LOADED FLOW DIAGRAMS ===');
        console.log('Flow diagrams loaded:', diagrams.length);
        diagrams.forEach((diagram, index) => {
          const flowId = diagram.flow_diagram_uuid || diagram.uuid || `api-flow-${index}`;
          console.log(`Flow ${index}: ID=${flowId}, Name=${diagram.name}, Tag=${diagram.tag}`);
        });
      } catch (error) {
        console.error('Error loading flow diagrams:', error);
        toast({
          title: "Error Loading Flow Diagrams",
          description: "Could not load saved flow diagrams. You can still create new ones.",
          variant: "destructive"
        });
      } finally {
        setIsLoadingFlowDiagrams(false);
      }
    };

    if (industryId) {
      loadFlowDiagrams();
    }
  }, [industryId, toast]);

  React.useEffect(() => {
    if (entryNodeFromState && flowTypeFromState) {
      setNodes([
        {
          ...entryNodeFromState,
          position: { x: 420, y: 200 },
          selected: false,
        }
      ]);
      setEdges([]);
      setShowModeDialog(false);
      setFlowMode("scratch");
      setTimeout(() => {
        if (reactFlowRef.current?.fitView) reactFlowRef.current.fitView({ padding: 0.12 });
      }, 100);
    }
  }, [entryNodeFromState, flowTypeFromState]);

  React.useEffect(() => {
    if (reactFlowRef.current && typeof reactFlowRef.current.fitView === 'function') {
      setTimeout(() => reactFlowRef.current.fitView({ padding: 0.12 }), 100);
    }
  }, [nodes]);

  React.useEffect(() => {
    // This useEffect is now disabled - template loading is handled in handleModeSelect
    // to ensure form data drives the visual diagram
    if (flowMode === "scratch" && !entryNodeFromState) {
      setNodes([]);
      setEdges([]);
      setInventoryNodes([]);
      setInventoryEdges([]);
    }
  }, [flowMode]);

  const [showConnectionDialog, setShowConnectionDialog] = useState(false);
  const [connectionSourceNode, setConnectionSourceNode] = useState<any>(null);
  const [targetNodeId, setTargetNodeId] = useState<string | null>(null);
  const [autoFillInputs, setAutoFillInputs] = useState<any[]>([]);
  const [availableTargetNodes, setAvailableTargetNodes] = useState<any[]>([]);

  // New state for incoming connection data
  const [incomingConnectionData, setIncomingConnectionData] = useState<any>(null);

  // State for storing completed form data for persistence
  const [nodeFormData, setNodeFormData] = useState<Record<string, any>>({});

  const [scenarioName, setScenarioName] = useState('My Inventory');
  const [editingScenarioName, setEditingScenarioName] = useState(false);

  const [selectedNodeId, setSelectedNodeId] = useState<string | null>(null);

  const [isLayingOut, setIsLayingOut] = useState(false);

  const [editNode, setEditNode] = useState<{ id: string; label: string } | null>(null);
  const [editEdge, setEditEdge] = useState<{ id: string; label: string } | null>(null);
  const [popoverOpen, setPopoverOpen] = useState(false);
  
  // New state for Add Constraints dialog
  const [showConstraintsModal, setShowConstraintsModal] = useState(false);

  // State for tracking constraints and scenario progress
  const [hasConstraints, setHasConstraints] = useState(false);
  const [hasRunScenario, setHasRunScenario] = useState(false);
  const [isRunningScenario, setIsRunningScenario] = useState(false);

  // New state for delete confirmation dialog
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [nodeToDelete, setNodeToDelete] = useState<string | null>(null);

  // New state for dual-tab editor and scenario comparison
  const [dualTabEditorOpen, setDualTabEditorOpen] = useState(false);
  const [dualTabNodeId, setDualTabNodeId] = useState<string | null>(null);
  const [scenarioComparisonOpen, setScenarioComparisonOpen] = useState(false);
  const [resultsBoardOpen, setResultsBoardOpen] = useState(false);

  // State for overwrite confirmation dialog
  const [showOverwriteDialog, setShowOverwriteDialog] = useState(false);
  const [pendingSaveData, setPendingSaveData] = useState<any>(null);
  const [pendingSaveResolver, setPendingSaveResolver] = useState<((value: boolean) => void) | null>(null);

  // State for scenario parameters
  const [scenarioParameters, setScenarioParameters] = useState<{
    period_type?: string;
    period_length?: number;
    number_of_periods?: number;
    start_date?: string;
    base_scenario_id?: string;
  }>({});

  // State for base scenario data (read-only in dual-tab editor)
  const [baseScenarioData, setBaseScenarioData] = useState<Record<string, any>>({});

  // State for base scenario name
  const [baseScenarioName, setBaseScenarioName] = useState<string>('');

  // New unified dual tab state management
  const [scenarioTabMode, setScenarioTabMode] = useState<'base' | 'current'>('current');
  const [baseScenarioFlowData, setBaseScenarioFlowData] = useState<{
    nodes: any[];
    edges: any[];
    formData: Record<string, any>;
    name: string;
  } | null>(null);

  // Helper function to get node technology and outputs
  const getNodeTechnologyAndOutputs = (nodeId) => {
    const node = nodes.find(n => n.id === nodeId);
    if (!node || !node.data) return null;
    
    return {
      nodeName: node.data.label || node.id,
      technology: node.data.technology || "",
      outputs: node.data.outputs || {}
    };
  };

  // Unified function to load base scenario data for dual tab system
  const loadBaseScenarioForDualTab = async (baseScenarioId: string) => {
    try {
      console.log('=== LOADING BASE SCENARIO FOR DUAL TAB ===');
      console.log('Base scenario ID:', baseScenarioId);

      // 1. Load complete base scenario data using existing function
      const baseData = await loadFlowDiagramAndReturnData(baseScenarioId);

      if (!baseData) {
        console.warn('No base scenario data loaded');
        return { success: false };
      }

      console.log('Base scenario data loaded:', {
        hasFormData: !!baseData.formData,
        hasFlowSchema: !!baseData.flowSchema,
        formDataKeys: baseData.formData ? Object.keys(baseData.formData) : [],
        flowSchemaKeys: baseData.flowSchema ? Object.keys(baseData.flowSchema) : []
      });

      // 2. Generate nodes and edges using existing logic
      let baseNodes: any[] = [];
      let baseEdges: any[] = [];

      if (baseData.flowSchema) {
        const generated = generateFlowFromAnySchema(baseData.flowSchema);
        baseNodes = generated.nodes;
        baseEdges = generated.edges;
      }

      // 3. Find the base scenario name
      const baseFlow = flowDiagrams.find(fd => fd.uuid === baseScenarioId);
      const baseScenarioDisplayName = baseFlow?.name || 'Base Scenario';

      // 4. Store as base scenario reference
      setBaseScenarioFlowData({
        nodes: baseNodes,
        edges: baseEdges,
        formData: baseData.formData || {},
        name: baseScenarioDisplayName
      });

      // 5. Store base scenario data for dual tab editor (legacy compatibility)
      setBaseScenarioData(baseData.formData || {});
      setBaseScenarioName(baseScenarioDisplayName);

      // 6. Initialize current scenario as copy of base
      setNodes([...baseNodes]);
      setEdges([...baseEdges]);
      setNodeFormData({ ...baseData.formData });

      console.log('Base scenario loaded successfully:', {
        nodes: baseNodes.length,
        edges: baseEdges.length,
        formDataKeys: Object.keys(baseData.formData || {}),
        name: baseScenarioDisplayName
      });

      return {
        success: true,
        nodes: baseNodes,
        edges: baseEdges,
        formData: baseData.formData || {}
      };
    } catch (error) {
      console.error('Error loading base scenario for dual tab:', error);
      return { success: false };
    }
  };

  // Helper functions for dual tab data access
  const getDisplayDataForTab = (nodeId: string, tab: 'base' | 'current') => {
    if (tab === 'base') {
      return baseScenarioFlowData?.formData[nodeId] || baseScenarioData[nodeId] || {};
    } else {
      return nodeFormData[nodeId] || {};
    }
  };

  const getDisplayNodesForTab = (tab: 'base' | 'current') => {
    if (tab === 'base' && baseScenarioFlowData) {
      return baseScenarioFlowData.nodes;
    } else {
      return nodes;
    }
  };

  const getDisplayEdgesForTab = (tab: 'base' | 'current') => {
    if (tab === 'base' && baseScenarioFlowData) {
      return baseScenarioFlowData.edges;
    } else {
      return edges;
    }
  };

  // Function to handle scenario tab switching
  const handleScenarioTabSwitch = (tab: 'base' | 'current') => {
    console.log('Switching scenario tab to:', tab);
    setScenarioTabMode(tab);

    // If switching to base tab and we have base scenario data, temporarily show base scenario flow
    if (tab === 'base' && baseScenarioFlowData) {
      console.log('Temporarily showing base scenario flow for comparison');
      // Note: In the future, we might want to temporarily switch the display
      // For now, we'll handle this in the dual tab editor
    }
  };

  // Function to create a new scenario from the current inventory
  const handleCreateScenario = async (scenarioData?: any) => {
    if (!scenarioData) return;

    try {
      console.log('=== CREATING SCENARIO ===');
      console.log('Scenario data received:', scenarioData);
      console.log('Base scenario ID from scenarioData:', scenarioData.baseScenario);
      console.log('Available flow diagrams:', flowDiagrams.map(fd => ({
        uuid: fd.uuid,
        flow_diagram_uuid: fd.flow_diagram_uuid,
        name: fd.name,
        tag: fd.tag
      })));

      // Store the current inventory state if not already in scenario mode
      // CRITICAL: Store this BEFORE loading base scenario to preserve current state
      if (!isScenarioMode) {
        console.log('Storing current inventory state before creating scenario');
        setInventoryNodes([...nodes]);
        setInventoryEdges([...edges]);
        console.log('Stored inventory nodes:', nodes.length, 'edges:', edges.length);
      }

      // Get base scenario data if specified - using unified dual tab loading
      let baseScenarioFlowDiagram = null;
      let shouldUseBaseScenarioFlow = false;
      let baseScenarioDataForSaving: { nodes: any[], edges: any[], formData: Record<string, any> } | null = null;

      if (scenarioData.baseScenario && scenarioData.baseScenario !== 'scratch') {
        console.log('=== SEARCHING FOR BASE SCENARIO ===');
        console.log('Looking for base scenario:', scenarioData.baseScenario);
        console.log('Available flow diagrams for search:', flowDiagrams.map((flow, index) => ({
          uuid: flow.uuid,
          flow_diagram_uuid: flow.flow_diagram_uuid,
          flowId: flow.flow_diagram_uuid || flow.uuid || `api-flow-${index}`,
          name: flow.name,
          matches: (flow.flow_diagram_uuid || flow.uuid || `api-flow-${index}`) === scenarioData.baseScenario
        })));

        // Find the base scenario from flow diagrams (API data)
        // Need to account for fallback IDs used in the modal
        const baseFlow = flowDiagrams.find((flow, index) => {
          const flowId = flow.flow_diagram_uuid || flow.uuid || `api-flow-${index}`;
          return flowId === scenarioData.baseScenario;
        });

        console.log('Base flow found:', baseFlow ? 'YES' : 'NO');
        if (baseFlow) {
          console.log('Base flow details:', baseFlow);
          baseScenarioFlowDiagram = baseFlow;

          // Use unified base scenario loading function
          try {
            const baseFlowIndex = flowDiagrams.findIndex((flow, index) => {
              const flowId = flow.flow_diagram_uuid || flow.uuid || `api-flow-${index}`;
              return flowId === scenarioData.baseScenario;
            });
            const baseFlowId = baseFlow.flow_diagram_uuid || baseFlow.uuid || `api-flow-${baseFlowIndex}`;

            const loadResult = await loadBaseScenarioForDualTab(baseFlowId);

            if (loadResult.success) {
              shouldUseBaseScenarioFlow = true;
              console.log('Base scenario loaded successfully using unified function');
              // Store the base scenario data for saving
              baseScenarioDataForSaving = {
                nodes: loadResult.nodes,
                edges: loadResult.edges,
                formData: loadResult.formData
              };
            } else {
              console.warn('Failed to load base scenario using unified function');
              shouldUseBaseScenarioFlow = false;
            }

          } catch (loadError) {
            console.error('Error loading base scenario:', loadError);
            console.error('Base scenario loading failed, will use current flow');
            shouldUseBaseScenarioFlow = false;
            toast({
              title: "Warning",
              description: "Could not load base scenario data. Creating scenario from current flow.",
              variant: "destructive"
            });
          }
        } else {
          // Check if it's a local saved flow
          const localFlow = savedFlows.find(flow => flow.title === scenarioData.baseScenario);
          if (localFlow) {
            console.log('Found local base flow:', localFlow);
            // Load local flow data
            setNodes(localFlow.nodes);
            setEdges(localFlow.edges);
            // For local flows, we don't have detailed form data, so base scenario will be empty
            setBaseScenarioData({});
            // Store the base scenario name from local flow
            setBaseScenarioName(localFlow.title || 'Base Scenario');
          } else {
            console.error('=== BASE SCENARIO NOT FOUND ===');
            console.error('Searched for:', scenarioData.baseScenario);
            console.error('Available options were:', flowDiagrams.map(flow => ({
              uuid: flow.uuid,
              name: flow.name
            })));
            shouldUseBaseScenarioFlow = false;
            toast({
              title: "Warning",
              description: "Base scenario not found. Creating scenario from current flow.",
              variant: "destructive"
            });
          }
        }
      } else {
        console.log('Creating scenario from scratch');
        // Clear base scenario data for scratch scenarios
        setBaseScenarioData({});
        // Clear base scenario name for scratch scenarios
        setBaseScenarioName('');
        // For scratch scenarios, we keep the current flow
        shouldUseBaseScenarioFlow = false;
      }

      // Create a new scenario
      setIsScenarioMode(true);
      console.log('Setting scenario name to:', scenarioData.scenarioName);
      setScenarioName(scenarioData.scenarioName);

      // CRITICAL FIX: Only use current flow if we're creating from scratch
      // If we loaded a base scenario, the nodes/edges are already set above
      if (!shouldUseBaseScenarioFlow && scenarioData.baseScenario === 'scratch') {
        console.log('Using current flow for scratch scenario');
        // Current nodes/edges are already in state, no need to change them
      } else if (!shouldUseBaseScenarioFlow) {
        console.warn('Base scenario loading failed, falling back to current flow');
        // Current nodes/edges are already in state, no need to change them
      } else {
        console.log('Successfully using base scenario flow for new scenario');
      }

      // Store scenario parameters for later use when saving
      // Only include base_scenario_id if it's a valid flow_diagram_uuid (not a fallback ID)
      let validBaseScenarioId = null;
      if (scenarioData.baseScenario && scenarioData.baseScenario !== 'scratch' && baseScenarioFlowDiagram) {
        console.log('=== BASE SCENARIO ID EXTRACTION DEBUG ===');
        console.log('baseScenarioFlowDiagram:', baseScenarioFlowDiagram);
        console.log('Available fields:', Object.keys(baseScenarioFlowDiagram));
        console.log('flow_diagram_uuid:', baseScenarioFlowDiagram.flow_diagram_uuid);
        console.log('uuid:', baseScenarioFlowDiagram.uuid);

        // Use the flow_diagram_uuid if we found a valid base scenario
        if (baseScenarioFlowDiagram.flow_diagram_uuid) {
          validBaseScenarioId = baseScenarioFlowDiagram.flow_diagram_uuid;
          console.log('Using flow_diagram_uuid as base scenario ID:', validBaseScenarioId);
        } else {
          // Fallback to uuid if flow_diagram_uuid is not available
          validBaseScenarioId = baseScenarioFlowDiagram.uuid;
          console.log('Fallback: Using uuid as base scenario ID:', validBaseScenarioId);
        }
        console.log('==========================================');
      }

      setScenarioParameters({
        period_type: scenarioData.period_type,
        period_length: scenarioData.period_length,
        number_of_periods: scenarioData.number_of_periods,
        start_date: scenarioData.start_date,
        base_scenario_id: validBaseScenarioId
      });

      // Show toast notification
      const baseDescription = shouldUseBaseScenarioFlow && baseScenarioFlowDiagram
        ? ` based on ${baseScenarioFlowDiagram.name}`
        : (scenarioData.baseScenario === 'scratch' ? ' from scratch' : ' from current flow');

      toast({
        title: "Scenario Created",
        description: `Created new scenario: ${scenarioData.scenarioName}${baseDescription}`
      });

      // Automatically save the scenario to the database
      console.log('=== AUTO-SAVING SCENARIO TO DATABASE ===');
      if (baseScenarioDataForSaving) {
        console.log('Using base scenario data for saving');
        await saveScenarioToDatabase(
          scenarioData,
          scenarioData.scenarioName,
          baseScenarioDataForSaving.nodes,
          baseScenarioDataForSaving.edges,
          baseScenarioDataForSaving.formData,
          validBaseScenarioId
        );
      } else {
        console.log('Using current state data for saving');
        await saveScenarioToDatabase(scenarioData, scenarioData.scenarioName, undefined, undefined, undefined, validBaseScenarioId);
      }

    } catch (error) {
      console.error('Error creating scenario:', error);
      toast({
        title: "Error Creating Scenario",
        description: "Failed to create scenario. Please try again.",
        variant: "destructive"
      });
    }
  };
  
  // Function to handle adding constraints to a scenario
  const handleAddConstraints = () => {
    if (isScenarioMode) {
      setShowConstraintsModal(true);
    }
  };

  // Function to handle constraint completion
  const handleConstraintsAdded = (constraints: any) => {
    console.log('Constraints added:', constraints);
    setHasConstraints(true);
    setShowConstraintsModal(false);
    toast({
      title: "Constraints Added",
      description: "Your scenario constraints have been successfully configured.",
    });
  };

  // Function to handle running scenario
  const handleRunScenario = () => {
    if (hasConstraints && !isRunningScenario) {
      setIsRunningScenario(true);
      setScenarioComparisonOpen(true);
    }
  };

  // Function to handle viewing results
  const handleViewResults = () => {
    if (hasRunScenario) {
      setResultsBoardOpen(true);
    }
  };

  // Function to handle overwrite confirmation
  const handleOverwriteConfirmation = (): Promise<boolean> => {
    return new Promise((resolve) => {
      setPendingSaveResolver(() => resolve);
      setShowOverwriteDialog(true);
    });
  };

  const handleOverwriteConfirm = () => {
    if (pendingSaveResolver) {
      pendingSaveResolver(true);
      setPendingSaveResolver(null);
    }
    setShowOverwriteDialog(false);
  };

  const handleOverwriteCancel = () => {
    if (pendingSaveResolver) {
      pendingSaveResolver(false);
      setPendingSaveResolver(null);
    }
    setShowOverwriteDialog(false);
  };

  // Functions for saving and loading flows
  const handleSaveFlow = () => {
    if (!scenarioName.trim()) {
      toast({
        title: 'Cannot Save',
        description: 'Please provide a name for this flow.',
        variant: 'destructive'
      });
      return;
    }

    const newFlow = {
      title: scenarioName,
      nodes: nodes,
      edges: edges,
      flowType: isScenarioMode ? 'scenario' : 'inventory' as 'inventory' | 'scenario',
      nodeFormData: nodeFormData // Include form data for proper restoration
    };

    // Check for existing flows with the same title
    const existingFlowIndex = savedFlows.findIndex(flow => flow.title === scenarioName);
    let updatedFlows;

    if (existingFlowIndex >= 0) {
      // Overwrite existing flow
      updatedFlows = [...savedFlows];
      updatedFlows[existingFlowIndex] = newFlow;
      toast({ 
        title: 'Flow Updated', 
        description: `Updated "${scenarioName}" in your inventory.`
      });
    } else {
      // Add new flow
      updatedFlows = [...savedFlows, newFlow];
      toast({ 
        title: 'Flow Saved', 
        description: `Added "${scenarioName}" to your inventory.`
      });
    }

    // If we're in subflow mode, offer to return to the main flow
    if (isSubflowMode && originalNodes.length > 0) {
      // Ask user if they want to return to the main flow
      // For now, we'll just exit subflow mode
      exitSubflowMode();
    }

    // Update state and save to localStorage
    setSavedFlows(updatedFlows);
    try {
      localStorage.setItem('savedFlows', JSON.stringify(updatedFlows));
    } catch (error) {
      console.error('Error saving flows to localStorage:', error);
      toast({
        title: 'Save Error', 
        description: 'Could not save flow to local storage.',
        variant: 'destructive'
      });
    }
  };

  // Function to convert current data to ultra-optimized structure
  const createUltraOptimizedFlowSchema = (
    overrideNodes?: any[],
    overrideNodeFormData?: Record<string, any>
  ): UltraOptimizedFlowSchema => {
    // Use override data if provided, otherwise use state variables
    const nodesToProcess = overrideNodes || nodes;
    const formDataToProcess = overrideNodeFormData || nodeFormData;

    console.log('=== CREATING ULTRA-OPTIMIZED SCHEMA ===');
    console.log('Nodes to process:', nodesToProcess.length);
    console.log('Node form data keys:', Object.keys(formDataToProcess));
    console.log('Using override data:', {
      nodes: !!overrideNodes,
      formData: !!overrideNodeFormData
    });

    const optimizedNodes: Record<string, UltraOptimizedNode> = {};

    // Process each node to create ultra-optimized structure
    nodesToProcess.forEach(node => {
      console.log(`Processing node ${node.id}:`, {
        type: node.type,
        label: node.data.label,
        hasFormData: !!formDataToProcess[node.id]
      });

      // Skip virtual input nodes and final output nodes
      if (node.type === 'input' || node.type === 'finalOutput') {
        console.log(`Skipping ${node.type} node ${node.id}`);
        return;
      }

      const existingFormData = formDataToProcess[node.id];
      const currentTechnology = existingFormData?.formData?.currentTechnology ||
                               existingFormData?.formData?.technology || 'Boiler';

      console.log(`Node ${node.id} technology:`, currentTechnology);
      console.log(`Node ${node.id} form data:`, existingFormData);

      // Create ultra-optimized technologies array
      const technologies: UltraOptimizedTechnology[] = [];

      if (existingFormData) {
        // Get technology-specific form data
        const techFormData = existingFormData.technologyFormData?.[currentTechnology];
        console.log(`Node ${node.id} tech form data:`, techFormData);

        // Convert material inputs with proper validation and filter out empty entries
        const rawMaterialInputs = techFormData?.materialInputs || existingFormData.formData?.materialInputs || [];
        const materialInputs: UltraOptimizedMaterialInput[] = Array.isArray(rawMaterialInputs) ? rawMaterialInputs
          .filter((input: any) => input.material && input.material.trim() !== '') // Filter out empty material inputs
          .map((input: any) => ({
            id: input.id || `mat-${Date.now()}`,
            material: input.material || '',
            unit: input.unit || '',
            quantity: parseFloat(input.quantity) || 1,
            cost: parseFloat(input.cost) || 0,
            specificMaterialCost: parseFloat(input.specificMaterialCost || input.smc) || 0,
            sourceNodeId: input.sourceActivity === 'Nil' ? undefined : input.sourceActivity,
            sourceTechnology: input.sourceActivity === 'Nil' ? undefined : input.technology,
            lowerBound: input.lowerBound ? parseFloat(input.lowerBound) : undefined,
            upperBound: input.upperBound ? parseFloat(input.upperBound) : undefined
          })) : [];

        // Convert energy inputs with proper validation and filter out empty entries
        const rawEnergyInputs = techFormData?.energyInputs || existingFormData.formData?.energyInputs || [];
        const energyInputs: UltraOptimizedEnergyInput[] = Array.isArray(rawEnergyInputs) ? rawEnergyInputs
          .filter((input: any) => {
            const energyName = input.source || input.energy || '';
            return energyName && energyName.trim() !== ''; // Filter out empty energy inputs
          })
          .map((input: any) => ({
            id: input.id || `eng-${Date.now()}`,
            energy: input.source || input.energy || '', // Fix: Use 'source' field from legacy format, fallback to 'energy'
            unit: input.unit || '',
            quantity: parseFloat(input.quantity) || 1,
            cost: parseFloat(input.cost) || 0,
            specificEnergyCost: parseFloat(input.specificEnergyCost || input.sec) || 0,
            sourceNodeId: input.sourceActivity === 'Nil' ? undefined : input.sourceActivity,
            sourceTechnology: input.sourceActivity === 'Nil' ? undefined : input.technology,
            lowerBound: input.lowerBound ? parseFloat(input.lowerBound) : undefined,
            upperBound: input.upperBound ? parseFloat(input.upperBound) : undefined
          })) : [];

        // Convert emissions with proper validation and filter out empty entries
        const rawEmissions = techFormData?.emissions || existingFormData.formData?.emissions || [];
        const emissions: UltraOptimizedEmissionInput[] = Array.isArray(rawEmissions) ? rawEmissions
          .filter((emission: any) => {
            const emissionName = emission.source || emission.emission || '';
            return emissionName && emissionName.trim() !== ''; // Filter out empty emissions
          })
          .map((emission: any) => ({
            id: emission.id || `em-${Date.now()}`,
            emission: emission.source || emission.emission || '', // Fix: Use 'source' field from legacy format, fallback to 'emission'
            unit: emission.unit || '',
            emissionFactor: parseFloat(emission.emissionFactor || emission.factor || emission.ef) || 0, // Fix: Add 'factor' field mapping
            cost: parseFloat(emission.cost) || 0
          })) : [];

        // Convert material outputs with proper validation
        const rawMaterialOutputs = extractMaterialOutputs(existingFormData);
        const materialOutputs: UltraOptimizedMaterialOutput[] = Array.isArray(rawMaterialOutputs) ? rawMaterialOutputs.map((output: any) => ({
          id: output.id || `out-${Date.now()}`,
          material: output.material || '',
          unit: output.unit || '',
          quantity: parseFloat(output.quantity) || 1,
          specificMaterialCost: parseFloat(output.specificMaterialCost) || 0, // Include SMC field
          isFinalOutput: output.isFinalOutput || false,
          targetNodeId: output.connectToNode || null,
          targetTechnology: output.destinationTechnology || null
        })) : [];

        // Convert energy outputs with proper validation
        const rawEnergyOutputs = extractEnergyOutputs(existingFormData);
        const energyOutputs: UltraOptimizedEnergyOutput[] = Array.isArray(rawEnergyOutputs) ? rawEnergyOutputs.map((output: any) => ({
          id: output.id || `eng-out-${Date.now()}`,
          energy: output.energy || '',
          unit: output.unit || '',
          quantity: parseFloat(output.quantity) || 1,
          specificEnergyCost: parseFloat(output.specificEnergyCost) || 0, // Include SEC field
          isFinalOutput: output.isFinalOutput || false,
          targetNodeId: output.connectToNode || null,
          targetTechnology: output.destinationTechnology || null
        })) : [];

        // Convert material byproducts with proper validation
        const rawMaterialByproducts = techFormData?.materialByProducts || existingFormData.formData?.materialByProducts || [];
        const materialByproducts: UltraOptimizedMaterialByproduct[] = Array.isArray(rawMaterialByproducts) ? rawMaterialByproducts.map((byproduct: any) => ({
          id: byproduct.id || `bp-${Date.now()}`,
          material: byproduct.byproduct || '',
          unit: byproduct.unit || '',
          quantity: parseFloat(byproduct.byproductPerPrimaryOutput || byproduct.bppo) || 1,
          specificMaterialCost: 0, // Byproducts typically don't have cost
          isFinalOutput: false,
          targetNodeId: byproduct.connectToNode || null,
          targetTechnology: null
        })) : [];

        // Convert energy byproducts with proper validation
        const rawEnergyByproducts = techFormData?.energyByProducts || existingFormData.formData?.energyByProducts || [];
        const energyByproducts: UltraOptimizedEnergyByproduct[] = Array.isArray(rawEnergyByproducts) ? rawEnergyByproducts.map((byproduct: any) => ({
          id: byproduct.id || `eng-bp-${Date.now()}`,
          energy: byproduct.byproduct || '',
          unit: byproduct.unit || '',
          quantity: parseFloat(byproduct.byproductPerPrimaryOutput || byproduct.bppo) || 1,
          specificEnergyCost: 0, // Byproducts typically don't have cost
          isFinalOutput: false,
          targetNodeId: byproduct.connectToNode || null,
          targetTechnology: null
        })) : [];

        // Convert financial data
        const financial: UltraOptimizedFinancial = {
          capacity: parseFloat(techFormData?.financial?.capacity || existingFormData.formData?.financial?.capacity) || 0,
          capacityUnit: techFormData?.financial?.capacityUnit || existingFormData.formData?.financial?.capacityUnit || 'Units/day',
          // Use the standardized capitalCost field with backward compatibility
          capitalCost: parseFloat(
            techFormData?.financial?.capitalCost ||
            existingFormData.formData?.financial?.capitalCost ||
            techFormData?.financial?.capitalCostUnit || // Backward compatibility
            existingFormData.formData?.financial?.capitalCostUnit
          ) || 0,
          capitalCostUnit: 'USD', // Default unit for capital cost
          operatingMaintenanceCost: parseFloat(
            techFormData?.financial?.operatingMaintenanceCost ||
            techFormData?.financial?.operatingAndMaintenanceCost ||
            techFormData?.financial?.omCost || // Backward compatibility
            existingFormData.formData?.financial?.operatingAndMaintenanceCost ||
            existingFormData.formData?.financial?.omCost
          ) || 0,
          operatingMaintenanceCostUnit: 'USD/year'
        };

        technologies.push({
          name: currentTechnology,
          isCustom: existingFormData.formData?.customTechnology ? true : false,
          startYear: parseInt(existingFormData.formData?.startYear) || new Date().getFullYear(),
          endYear: parseInt(existingFormData.formData?.endYear) || new Date().getFullYear() + 10,
          emission: parseFloat(techFormData?.technologyEmission || existingFormData.formData?.technologyEmission) || undefined,
          inputs: {
            materials: materialInputs,
            energies: energyInputs,
            emissions: emissions
          },
          outputs: {
            materials: materialOutputs,
            energies: energyOutputs
          },
          byproducts: {
            materials: materialByproducts,
            energies: energyByproducts
          },
          financial: financial
        });
      } else {
        // Default empty technology data
        technologies.push({
          name: currentTechnology,
          isCustom: false,
          startYear: new Date().getFullYear(),
          endYear: new Date().getFullYear() + 10,
          inputs: { materials: [], energies: [], emissions: [] },
          outputs: { materials: [], energies: [] },
          byproducts: { materials: [], energies: [] },
          financial: {
            capacity: 0,
            capacityUnit: 'Units/day',
            capitalCost: 0,
            capitalCostUnit: 'USD',
            operatingMaintenanceCost: 0,
            operatingMaintenanceCostUnit: 'USD/year'
          }
        });
      }

      optimizedNodes[node.id] = {
        activity: node.data.label,
        position: node.position,
        technologies: technologies
      };
    });

    return {
      nodes: optimizedNodes,
      metadata: {
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    };
  };

  // Helper function to extract material outputs from legacy structure
  const extractMaterialOutputs = (formData: any): MaterialOutput[] => {
    const materialOutputs: MaterialOutput[] = [];

    // First try to extract from legacy outputs structure (this is the primary source)
    const outputs = formData.outputs || [];

    outputs.forEach((output: any) => {
      const matOutputs = output.materialOutputs || output.matOutputs || [];
      matOutputs.forEach((matOut: any) => {
        materialOutputs.push({
          id: matOut.id,
          material: matOut.material,
          unit: matOut.unit,
          specificMaterialCost: matOut.smc || matOut.specificMaterialCost || '',
          isFinalOutput: matOut.final || matOut.isFinalOutput || false,
          connectToNode: matOut.connect || matOut.connectToNode || '',
          quantity: matOut.qty || matOut.quantity || '',
          quantityUnit: matOut.qtyUnit || matOut.quantityUnit || '',
          destinationTechnology: matOut.destinationTechnology
        });
      });
    });

    // Only check technology-specific form data if no outputs found in legacy structure
    if (materialOutputs.length === 0) {
      const technologyFormData = formData.technologyFormData || {};
      Object.values(technologyFormData).forEach((techData: any) => {
        const techMaterialOutputs = techData.materialOutputs || [];
        techMaterialOutputs.forEach((matOut: any) => {
          materialOutputs.push({
            id: matOut.id,
            material: matOut.material,
            unit: matOut.unit,
            specificMaterialCost: matOut.specificMaterialCost || matOut.smc || '',
            isFinalOutput: matOut.isFinalOutput || matOut.final || false,
            connectToNode: matOut.connectToNode || matOut.connect || '',
            quantity: matOut.quantity || matOut.qty || '',
            quantityUnit: matOut.quantityUnit || matOut.qtyUnit || '',
            destinationTechnology: matOut.destinationTechnology
          });
        });
      });
    }

    return materialOutputs;
  };

  // Helper function to extract energy outputs from legacy structure
  const extractEnergyOutputs = (formData: any): EnergyOutput[] => {
    const energyOutputs: EnergyOutput[] = [];

    // First try to extract from legacy outputs structure (this is the primary source)
    const outputs = formData.outputs || [];

    outputs.forEach((output: any) => {
      const energyOuts = output.energyOutputs || [];
      energyOuts.forEach((energyOut: any) => {
        energyOutputs.push({
          id: energyOut.id,
          energy: energyOut.energy,
          unit: energyOut.unit,
          specificEnergyCost: energyOut.sec || energyOut.specificEnergyCost || '',
          isFinalOutput: energyOut.final || energyOut.isFinalOutput || false,
          connectToNode: energyOut.connect || energyOut.connectToNode || '',
          quantity: energyOut.qty || energyOut.quantity || '',
          quantityUnit: energyOut.qtyUnit || energyOut.quantityUnit || '',
          destinationTechnology: energyOut.destinationTechnology
        });
      });
    });

    // Only check technology-specific form data if no outputs found in legacy structure
    if (energyOutputs.length === 0) {
      const technologyFormData = formData.technologyFormData || {};
      Object.values(technologyFormData).forEach((techData: any) => {
        const techEnergyOutputs = techData.energyOutputs || [];
        techEnergyOutputs.forEach((energyOut: any) => {
          energyOutputs.push({
            id: energyOut.id,
            energy: energyOut.energy,
            unit: energyOut.unit,
            specificEnergyCost: energyOut.specificEnergyCost || energyOut.sec || '',
            isFinalOutput: energyOut.isFinalOutput || energyOut.final || false,
            connectToNode: energyOut.connectToNode || energyOut.connect || '',
            quantity: energyOut.quantity || energyOut.qty || '',
            quantityUnit: energyOut.quantityUnit || energyOut.qtyUnit || '',
            destinationTechnology: energyOut.destinationTechnology
          });
        });
      });
    }

    return energyOutputs;
  };

  // Save scenario to database with API integration
  const saveScenarioToDatabase = async (
    scenarioData: any,
    userEnteredScenarioName?: string,
    overrideNodes?: any[],
    overrideEdges?: any[],
    overrideNodeFormData?: Record<string, any>,
    baseScenarioId?: string | null
  ) => {
    console.log('saveScenarioToDatabase called');
    console.log('scenarioData:', scenarioData);
    console.log('userEnteredScenarioName:', userEnteredScenarioName);
    console.log('scenarioName state:', scenarioName);
    console.log('isScenarioMode:', isScenarioMode);
    console.log('Override data provided:', {
      nodes: !!overrideNodes,
      edges: !!overrideEdges,
      formData: !!overrideNodeFormData
    });

    // Use the passed scenario name or fall back to the state
    const nameToUse = userEnteredScenarioName || scenarioName;

    if (!nameToUse.trim()) {
      console.log('No scenario name provided');
      toast({
        title: 'Cannot Save Scenario',
        description: 'Please provide a name for this scenario.',
        variant: 'destructive'
      });
      return;
    }

    try {
      console.log('Starting scenario save process...');

      // Try to create ultra-optimized flow schema, fallback to legacy if it fails
      let flowSchemaToSave: any;
      let schemaType = 'ultra-optimized';

      try {
        flowSchemaToSave = createUltraOptimizedFlowSchema(overrideNodes, overrideNodeFormData);
        console.log('=== ULTRA-OPTIMIZED SCENARIO SCHEMA ===');
        console.log('Schema structure:', {
          nodeCount: Object.keys(flowSchemaToSave.nodes).length,
          metadata: flowSchemaToSave.metadata
        });
        console.log('Full ultra-optimized schema:', flowSchemaToSave);
        console.log('=======================================');
      } catch (error) {
        console.warn('Ultra-optimized schema creation failed, falling back to legacy format:', error);
        schemaType = 'legacy';

        // Create legacy flow schema as fallback
        flowSchemaToSave = {
          name: nameToUse,
          flowType: 'scenario',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          nodeData: {},
          industryId: industryId
        };

        // Process nodes for legacy format
        const nodesToProcessLegacy = overrideNodes || nodes;
        const formDataToProcessLegacy = overrideNodeFormData || nodeFormData;

        nodesToProcessLegacy.forEach(node => {
          if (node.type === 'input' || node.type === 'finalOutput') {
            return;
          }

          const existingFormData = formDataToProcessLegacy[node.id];
          if (existingFormData) {
            flowSchemaToSave.nodeData[node.id] = {
              id: node.id,
              label: node.data.label,
              type: node.type,
              position: node.position,
              formData: existingFormData.formData || {},
              technologies: existingFormData.technologies || [],
              completedAt: existingFormData.completedAt || new Date().toISOString()
            };
          }
        });

        console.log('=== LEGACY SCENARIO SCHEMA (FALLBACK) ===');
        console.log('Schema structure:', {
          nodeCount: Object.keys(flowSchemaToSave.nodeData).length,
          flowType: flowSchemaToSave.flowType
        });
        console.log('Full legacy schema:', flowSchemaToSave);
        console.log('========================================');
      }

      // Get sector UUID for API call
      const sectorUuid = await getSectorUuidFromIndustryId(industryId || '');

      // Prepare data for API with scenario-specific fields
      const flowDiagramData = {
        sector_uuid: sectorUuid,
        name: nameToUse,
        tag: 'SCENARIO_MAIN' as const,
        flow_type: 'SCENARIO_MAIN' as const,
        flow_diagram: flowSchemaToSave,
        // Scenario-specific fields from the form
        period_type: (scenarioData.period_type || 'single') as 'single' | 'multiple',
        period_length: scenarioData.period_length || 12,
        number_of_periods: scenarioData.number_of_periods || 1,
        start_date: scenarioData.start_date || new Date().toISOString().split('T')[0],
        base_scenario_id: baseScenarioId || scenarioParameters.base_scenario_id || null
      };

      console.log('=== SCENARIO API PAYLOAD ===');
      console.log('Schema type:', schemaType);
      console.log('Scenario name being saved:', flowDiagramData.name);
      console.log('User entered name:', userEnteredScenarioName);
      console.log('State scenario name:', scenarioName);
      console.log('Name used:', nameToUse);
      console.log('Payload size:', JSON.stringify(flowDiagramData).length, 'characters');
      console.log('Flow diagram keys:', Object.keys(flowDiagramData.flow_diagram));
      console.log('Period type:', flowDiagramData.period_type);
      console.log('Period length:', flowDiagramData.period_length);
      console.log('Number of periods:', flowDiagramData.number_of_periods);
      console.log('Start date:', flowDiagramData.start_date);
      console.log('Base scenario ID:', flowDiagramData.base_scenario_id);
      console.log('Base scenario ID type:', typeof flowDiagramData.base_scenario_id);
      console.log('Base scenario ID is valid UUID:', flowDiagramData.base_scenario_id ? /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(flowDiagramData.base_scenario_id) : 'null');
      console.log('============================');

      // Call API to save or update scenario with duplicate name handling
      const savedFlowDiagram = await saveOrUpdateFlowDiagram(
        flowDiagramData,
        { toast },
        handleOverwriteConfirmation
      );

      if (savedFlowDiagram) {
        // Update local state with the new flow diagram
        setFlowDiagrams(prev => {
          // Remove any existing flow diagram with the same name to avoid duplicates
          const filtered = prev.filter(fd => fd.name !== savedFlowDiagram.name);
          return [...filtered, savedFlowDiagram];
        });
        setCurrentFlowDiagramUuid(savedFlowDiagram.uuid);

        console.log('Scenario saved successfully:', savedFlowDiagram);
        console.log(`${schemaType} schema used:`, flowSchemaToSave);

        toast({
          title: "Scenario Saved",
          description: `Scenario "${nameToUse}" has been saved to the database.`
        });
      }
    } catch (error) {
      console.error('Error saving scenario:', error);
      toast({
        title: 'Save Error',
        description: error instanceof Error ? error.message : 'Could not save scenario.',
        variant: 'destructive'
      });
    }
  };

  // Save function with API integration
  const saveFlowToDatabase = async () => {
    console.log('saveFlowToDatabase called');
    console.log('scenarioName:', scenarioName);
    console.log('isScenarioMode:', isScenarioMode);

    if (!scenarioName.trim()) {
      console.log('No scenario name provided');
      toast({
        title: 'Cannot Save',
        description: 'Please provide a name for this flow.',
        variant: 'destructive'
      });
      return;
    }

    try {
      console.log('Starting save process...');

      // Try to create ultra-optimized flow schema, fallback to legacy if it fails
      let flowSchemaToSave: any;
      let schemaType = 'ultra-optimized';

      try {
        flowSchemaToSave = createUltraOptimizedFlowSchema();
        console.log('=== ULTRA-OPTIMIZED FLOW SCHEMA ===');
        console.log('Schema structure:', {
          nodeCount: Object.keys(flowSchemaToSave.nodes).length,
          metadata: flowSchemaToSave.metadata
        });
        console.log('Nodes:', Object.entries(flowSchemaToSave.nodes).map(([id, node]: [string, any]) => ({
          id: id,
          activity: node.activity,
          technologiesCount: node.technologies.length,
          position: node.position
        })));
        console.log('Full ultra-optimized schema:', flowSchemaToSave);
        console.log('===================================');
      } catch (error) {
        console.warn('Ultra-optimized schema creation failed, falling back to legacy format:', error);
        schemaType = 'legacy';

        // Create legacy flow schema as fallback
        flowSchemaToSave = {
          name: scenarioName,
          flowType: isScenarioMode ? 'scenario' : 'inventory',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          nodeData: {},
          industryId: industryId
        };

        // Process nodes for legacy format
        nodes.forEach(node => {
          if (node.type === 'input' || node.type === 'finalOutput') {
            return;
          }

          const existingFormData = nodeFormData[node.id];
          if (existingFormData) {
            flowSchemaToSave.nodeData[node.id] = {
              id: node.id,
              label: node.data.label,
              type: node.type,
              position: node.position,
              formData: existingFormData.formData || {},
              technologies: existingFormData.technologies || [],
              completedAt: existingFormData.completedAt || new Date().toISOString()
            };
          }
        });

        console.log('=== LEGACY FLOW SCHEMA (FALLBACK) ===');
        console.log('Schema structure:', {
          nodeCount: Object.keys(flowSchemaToSave.nodeData).length,
          flowType: flowSchemaToSave.flowType
        });
        console.log('Full legacy schema:', flowSchemaToSave);
        console.log('====================================');
      }

      // Get sector UUID for API call
      const sectorUuid = await getSectorUuidFromIndustryId(industryId || '');

      // Determine tag based on current mode
      let tag: 'INVENTORY_MAIN' | 'SCENARIO_MAIN' = 'INVENTORY_MAIN';
      if (isScenarioMode) {
        tag = 'SCENARIO_MAIN';
      }

      // Prepare data for API with optimized or legacy schema
      const flowDiagramData = {
        sector_uuid: sectorUuid,
        name: scenarioName,
        tag: tag,
        flow_type: tag, // Required field at body level - same as tag
        flow_diagram: flowSchemaToSave,
        // Add scenario-specific fields if in scenario mode
        ...(isScenarioMode && {
          period_type: (scenarioParameters.period_type || 'single') as 'single' | 'multiple',
          period_length: scenarioParameters.period_length || 12,
          number_of_periods: scenarioParameters.number_of_periods || 1,
          start_date: scenarioParameters.start_date || new Date().toISOString().split('T')[0],
          base_scenario_id: scenarioParameters.base_scenario_id
        })
      };

      console.log('=== API PAYLOAD ===');
      console.log('Schema type:', schemaType);
      console.log('Payload size:', JSON.stringify(flowDiagramData).length, 'characters');
      console.log('Flow diagram keys:', Object.keys(flowDiagramData.flow_diagram));
      if (schemaType === 'ultra-optimized') {
        console.log('Nodes count:', Object.keys(flowDiagramData.flow_diagram.nodes || {}).length);
      } else {
        console.log('Nodes count:', Object.keys(flowDiagramData.flow_diagram.nodeData || {}).length);
      }
      console.log('==================');

      // Call API to save or update flow diagram with duplicate name handling
      const savedFlowDiagram = await saveOrUpdateFlowDiagram(
        flowDiagramData,
        { toast },
        handleOverwriteConfirmation
      );

      if (savedFlowDiagram) {
        // Update local state with the new flow diagram
        setFlowDiagrams(prev => {
          // Remove any existing flow diagram with the same name to avoid duplicates
          const filtered = prev.filter(fd => fd.name !== savedFlowDiagram.name);
          return [...filtered, savedFlowDiagram];
        });
        setCurrentFlowDiagramUuid(savedFlowDiagram.uuid);

        console.log('Flow diagram saved successfully:', savedFlowDiagram);
        console.log(`${schemaType} schema used:`, flowSchemaToSave);
      }
    } catch (error) {
      console.error('Error saving flow diagram:', error);
      toast({
        title: 'Save Error',
        description: error instanceof Error ? error.message : 'Could not save flow diagram.',
        variant: 'destructive'
      });
    }
  };

  // Function removed - no longer needed with API integration


  // Function to generate nodes and edges from ultra-optimized schema
  const generateFlowFromUltraOptimizedSchema = (schema: UltraOptimizedFlowSchema) => {
    console.log('=== GENERATING FLOW FROM ULTRA-OPTIMIZED SCHEMA ===');
    console.log('Schema structure:', schema);
    console.log('Schema nodes keys:', Object.keys(schema.nodes || {}));
    console.log('Schema metadata:', schema.metadata);

    // Validate schema structure
    if (!schema.nodes || typeof schema.nodes !== 'object') {
      console.error('Invalid schema: nodes is missing or not an object');
      return { nodes: [], edges: [] };
    }

    // Generate process nodes from ultra-optimized node data
    const generatedNodes = Object.entries(schema.nodes).map(([nodeId, nodeData]) => {
      console.log(`Processing node ${nodeId}:`, nodeData);

      // Validate node data structure
      if (!nodeData || !nodeData.position) {
        console.warn(`Node ${nodeId} missing position data, using default`);
        nodeData = {
          ...nodeData,
          position: { x: 100, y: 100 }
        };
      }

      if (!nodeData.technologies || !Array.isArray(nodeData.technologies) || nodeData.technologies.length === 0) {
        console.warn(`Node ${nodeId} missing technologies, using default`);
        nodeData = {
          ...nodeData,
          technologies: [{ name: 'Boiler' }]
        };
      }

      return {
        id: nodeId,
        type: 'custom', // Use the correct registered node type
        position: nodeData.position,
        data: {
          label: nodeData.activity || 'Unknown Activity',
          technology: nodeData.technologies[0]?.name || 'Boiler',
          outputs: {} // Will be populated from form data
        },
        style: {} // Default style
      };
    });

    console.log('Generated process nodes:', generatedNodes);

    // Track virtual input nodes and final output nodes to create
    const virtualInputNodes: any[] = [];
    const finalOutputNodes: any[] = [];

    // Generate edges from form data
    const generatedEdges: any[] = [];
    let edgeIdCounter = 0;

    // Process each node's technology data to generate edges
    Object.entries(schema.nodes).forEach(([nodeId, nodeData]) => {
      const currentTech = nodeData.technologies[0]; // Get first technology
      if (!currentTech) {
        console.log(`No technology found for node ${nodeId}`);
        return;
      }

      console.log(`Processing technology for node ${nodeId}:`, currentTech);

      // Generate standalone input edges from materialInputs where sourceNodeId is undefined
      currentTech.inputs?.materials?.forEach(input => {
        console.log(`Processing material input for node ${nodeId}:`, input);
        if (!input.sourceNodeId) {
          // Create virtual input node if it doesn't exist
          const virtualInputId = `virtual-input-${input.material.toLowerCase().replace(/\s+/g, '-')}`;

          // Check if virtual input node already exists
          if (!virtualInputNodes.find(n => n.id === virtualInputId) &&
              !generatedNodes.find(n => n.id === virtualInputId)) {
            virtualInputNodes.push({
              id: virtualInputId,
              type: 'input',
              position: { x: nodeData.position.x - 200, y: nodeData.position.y },
              data: { label: input.material },
              style: {
                width: 1,
                height: 1,
                opacity: 0,
                pointerEvents: 'none',
              }
            });
          }

          // Create standalone input edge
          generatedEdges.push({
            id: `input-${input.material.toLowerCase().replace(/\s+/g, '-')}-${nodeId}`,
            source: virtualInputId,
            sourceHandle: 'right-source',
            target: nodeId,
            targetHandle: 'left-target',
            label: input.material,
            style: {
              stroke: "#10B981", // Green color for input edges
              strokeWidth: 3,
              opacity: 0.9,
            },
            markerEnd: { type: MarkerType.ArrowClosed, color: "#10B981" },
            labelStyle: {
              fill: '#787882',
              fontWeight: 400,
              fontSize: 13.5,
            },
            type: 'smoothstep',
            data: {
              type: 'input-material',
              material: input.material,
              sourceActivity: 'Nil',
              technology: 'Nil',
              isInputEdge: true
            }
          });
        }
      });

      // Generate output edges from material outputs
      currentTech.outputs?.materials?.forEach(materialOutput => {
        console.log(`Processing material output for node ${nodeId}:`, materialOutput);
        if (materialOutput.targetNodeId && materialOutput.targetNodeId !== nodeId) {
          // Find target node data for positioning
          const targetNodeData = schema.nodes[materialOutput.targetNodeId];

          let sourceHandle = 'right-source';
          let targetHandle = 'left-target';
          const edgeType = 'smoothstep';

          if (targetNodeData) {
            const sourceY = nodeData.position.y;
            const targetY = targetNodeData.position.y;
            const sourceX = nodeData.position.x;
            const targetX = targetNodeData.position.x;

            // Calculate distances and angles for more natural routing
            const deltaX = targetX - sourceX;
            const deltaY = targetY - sourceY;
            const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
            const angle = Math.atan2(deltaY, deltaX) * (180 / Math.PI);

            // Smart routing logic that avoids intersections
            // Calculate the ratio to determine which direction is more significant
            const horizontalRatio = Math.abs(deltaX) / (Math.abs(deltaX) + Math.abs(deltaY));
            const verticalRatio = Math.abs(deltaY) / (Math.abs(deltaX) + Math.abs(deltaY));

            // Check if nodes are in similar rows/columns to avoid intersections
            const sameRow = Math.abs(deltaY) < 50; // Within 50px vertically
            const sameColumn = Math.abs(deltaX) < 50; // Within 50px horizontally

            // Special handling for nodes in the same row
            if (sameRow && Math.abs(deltaX) > 100) {
              // Horizontal flow for same-row nodes
              if (deltaX > 0) {
                sourceHandle = 'right-source';
                targetHandle = 'left-target';
              } else {
                sourceHandle = 'left-source';
                targetHandle = 'right-target';
              }
            }
            // Special handling for nodes in the same column
            else if (sameColumn && Math.abs(deltaY) > 100) {
              // Vertical flow for same-column nodes
              if (deltaY > 0) {
                sourceHandle = 'bottom-source';
                targetHandle = 'top-target';
              } else {
                sourceHandle = 'top-source';
                targetHandle = 'bottom-target';
              }
            }
            // For diagonal connections, prefer side connections to reduce intersections
            else if (deltaY > 100 && Math.abs(deltaX) > 50) {
              // Target is significantly below and to the side
              // Use side connection to avoid crossing other vertical flows
              if (Math.abs(deltaX) > Math.abs(deltaY) * 0.5) {
                // More horizontal component - use side connection
                if (deltaX > 0) {
                  sourceHandle = 'right-source';
                  targetHandle = 'left-target';
                } else {
                  sourceHandle = 'left-source';
                  targetHandle = 'right-target';
                }
              } else {
                // More vertical component - use vertical connection
                sourceHandle = 'bottom-source';
                targetHandle = 'top-target';
              }
            }
            // Default routing based on dominance
            else {
              const dominanceThreshold = 0.7;

              if (verticalRatio > dominanceThreshold) {
                // Strong vertical flow
                if (deltaY > 0) {
                  sourceHandle = 'bottom-source';
                  targetHandle = 'top-target';
                } else {
                  sourceHandle = 'top-source';
                  targetHandle = 'bottom-target';
                }
              } else if (horizontalRatio > dominanceThreshold) {
                // Strong horizontal flow
                if (deltaX > 0) {
                  sourceHandle = 'right-source';
                  targetHandle = 'left-target';
                } else {
                  sourceHandle = 'left-source';
                  targetHandle = 'right-target';
                }
              } else {
                // Mixed flow - choose the larger direction
                if (Math.abs(deltaY) > Math.abs(deltaX)) {
                  if (deltaY > 0) {
                    sourceHandle = 'bottom-source';
                    targetHandle = 'top-target';
                  } else {
                    sourceHandle = 'top-source';
                    targetHandle = 'bottom-target';
                  }
                } else {
                  if (deltaX > 0) {
                    sourceHandle = 'right-source';
                    targetHandle = 'left-target';
                  } else {
                    sourceHandle = 'left-source';
                    targetHandle = 'right-target';
                  }
                }
              }
            }
          }

          // Create material output edge
          generatedEdges.push({
            id: `e${nodeId}-${materialOutput.targetNodeId}-${edgeIdCounter++}`,
            source: nodeId,
            sourceHandle: sourceHandle,
            target: materialOutput.targetNodeId,
            targetHandle: targetHandle,
            label: materialOutput.material,
            style: {
              stroke: '#9b87f5',
              strokeWidth: 2,
              opacity: 0.85,
            },
            markerEnd: { type: MarkerType.ArrowClosed, color: "#9b87f5" },
            labelStyle: {
              fill: '#787882',
              fontWeight: 400,
              fontSize: 13.5,
            },
            type: edgeType,
            data: {
              type: 'material-output',
              material: materialOutput.material,
              isFinalOutput: materialOutput.isFinalOutput
            }
          });

          // If this is a final output, create a final output node
          if (materialOutput.isFinalOutput) {
            const finalOutputId = `final-output-${nodeId}-${materialOutput.material.toLowerCase().replace(/\s+/g, '-')}`;

            if (!finalOutputNodes.find(n => n.id === finalOutputId)) {
              finalOutputNodes.push({
                id: finalOutputId,
                type: 'finalOutput',
                position: {
                  x: nodeData.position.x,
                  y: nodeData.position.y + 300
                },
                data: {
                  label: materialOutput.material,
                  outputs: {
                    material: materialOutput.material,
                    materialUnit: materialOutput.unit
                  }
                },
                style: {}
              });

              // Create edge to final output
              generatedEdges.push({
                id: `final-${nodeId}-${finalOutputId}`,
                source: nodeId,
                sourceHandle: 'bottom-source',
                target: finalOutputId,
                targetHandle: 'top-target',
                label: materialOutput.material,
                style: {
                  stroke: '#f59e0b',
                  strokeWidth: 3,
                  opacity: 0.9,
                },
                markerEnd: { type: MarkerType.ArrowClosed, color: "#f59e0b" },
                labelStyle: {
                  fill: '#787882',
                  fontWeight: 400,
                  fontSize: 13.5,
                },
                type: 'smoothstep',
                data: {
                  type: 'final-output',
                  material: materialOutput.material
                }
              });
            }
          }
        }
      });

      // Generate output edges from energy outputs (similar logic)
      currentTech.outputs?.energies?.forEach(energyOutput => {
        console.log(`Processing energy output for node ${nodeId}:`, energyOutput);
        if (energyOutput.targetNodeId && energyOutput.targetNodeId !== nodeId) {
          const targetNodeData = schema.nodes[energyOutput.targetNodeId];

          let sourceHandle = 'right-source';
          let targetHandle = 'left-target';

          if (targetNodeData) {
            const sourceY = nodeData.position.y;
            const targetY = targetNodeData.position.y;
            const sourceX = nodeData.position.x;
            const targetX = targetNodeData.position.x;

            // Calculate distances for more natural routing
            const deltaX = targetX - sourceX;
            const deltaY = targetY - sourceY;

            // Smarter routing logic - choose the most direct path
            const horizontalRatio = Math.abs(deltaX) / (Math.abs(deltaX) + Math.abs(deltaY));
            const verticalRatio = Math.abs(deltaY) / (Math.abs(deltaX) + Math.abs(deltaY));
            const dominanceThreshold = 0.7;

            if (verticalRatio > dominanceThreshold) {
              // Strong vertical flow
              if (deltaY > 0) {
                sourceHandle = 'bottom-source';
                targetHandle = 'top-target';
              } else {
                sourceHandle = 'top-source';
                targetHandle = 'bottom-target';
              }
            } else if (horizontalRatio > dominanceThreshold) {
              // Strong horizontal flow
              if (deltaX > 0) {
                sourceHandle = 'right-source';
                targetHandle = 'left-target';
              } else {
                sourceHandle = 'left-source';
                targetHandle = 'right-target';
              }
            } else {
              // Mixed flow - choose the larger direction
              if (Math.abs(deltaY) > Math.abs(deltaX)) {
                if (deltaY > 0) {
                  sourceHandle = 'bottom-source';
                  targetHandle = 'top-target';
                } else {
                  sourceHandle = 'top-source';
                  targetHandle = 'bottom-target';
                }
              } else {
                if (deltaX > 0) {
                  sourceHandle = 'right-source';
                  targetHandle = 'left-target';
                } else {
                  sourceHandle = 'left-source';
                  targetHandle = 'right-target';
                }
              }
            }
          }

          generatedEdges.push({
            id: `e${nodeId}-${energyOutput.targetNodeId}-energy-${edgeIdCounter++}`,
            source: nodeId,
            sourceHandle: sourceHandle,
            target: energyOutput.targetNodeId,
            targetHandle: targetHandle,
            label: energyOutput.energy,
            style: {
              stroke: '#f59e0b',
              strokeWidth: 2,
              opacity: 0.85,
            },
            markerEnd: { type: MarkerType.ArrowClosed, color: "#f59e0b" },
            labelStyle: {
              fill: '#787882',
              fontWeight: 400,
              fontSize: 13.5,
            },
            type: 'smoothstep',
            data: {
              type: 'energy-output',
              energy: energyOutput.energy,
              isFinalOutput: energyOutput.isFinalOutput
            }
          });
        }
      });

      // Generate byproduct edges from material byproducts
      currentTech.byproducts?.materials?.forEach(byproduct => {
        console.log(`Processing material byproduct for node ${nodeId}:`, byproduct);
        if (byproduct.targetNodeId && byproduct.targetNodeId !== nodeId) {
          const targetNodeData = schema.nodes[byproduct.targetNodeId];

          let sourceHandle = 'right-source';
          let targetHandle = 'left-target';

          if (targetNodeData) {
            const sourceY = nodeData.position.y;
            const targetY = targetNodeData.position.y;
            const sourceX = nodeData.position.x;
            const targetX = targetNodeData.position.x;

            // Calculate distances for more natural routing
            const deltaX = targetX - sourceX;
            const deltaY = targetY - sourceY;

            // Smarter routing for byproducts - use the most direct path
            const horizontalRatio = Math.abs(deltaX) / (Math.abs(deltaX) + Math.abs(deltaY));
            const verticalRatio = Math.abs(deltaY) / (Math.abs(deltaX) + Math.abs(deltaY));
            const dominanceThreshold = 0.7;

            if (verticalRatio > dominanceThreshold) {
              // Strong vertical flow
              if (deltaY > 0) {
                sourceHandle = 'bottom-source';
                targetHandle = 'top-target';
              } else {
                sourceHandle = 'top-source';
                targetHandle = 'bottom-target';
              }
            } else if (horizontalRatio > dominanceThreshold) {
              // Strong horizontal flow
              if (deltaX > 0) {
                sourceHandle = 'right-source';
                targetHandle = 'left-target';
              } else {
                sourceHandle = 'left-source';
                targetHandle = 'right-target';
              }
            } else {
              // Mixed flow - choose the larger direction
              if (Math.abs(deltaY) > Math.abs(deltaX)) {
                if (deltaY > 0) {
                  sourceHandle = 'bottom-source';
                  targetHandle = 'top-target';
                } else {
                  sourceHandle = 'top-source';
                  targetHandle = 'bottom-target';
                }
              } else {
                if (deltaX > 0) {
                  sourceHandle = 'right-source';
                  targetHandle = 'left-target';
                } else {
                  sourceHandle = 'left-source';
                  targetHandle = 'right-target';
                }
              }
            }
          }

          generatedEdges.push({
            id: `byproduct-material-${nodeId}-${byproduct.targetNodeId}-${edgeIdCounter++}`,
            source: nodeId,
            sourceHandle: sourceHandle,
            target: byproduct.targetNodeId,
            targetHandle: targetHandle,
            label: byproduct.material,
            style: {
              stroke: '#f59e0b',
              strokeWidth: 2,
              opacity: 0.85,
              strokeDasharray: '5,5'
            },
            markerEnd: { type: MarkerType.ArrowClosed, color: "#f59e0b" },
            labelStyle: {
              fill: '#787882',
              fontWeight: 400,
              fontSize: 13.5,
            },
            type: 'smoothstep',
            data: {
              type: 'material-byproduct',
              material: byproduct.material
            }
          });
        }
      });

      // Generate byproduct edges from energy byproducts
      currentTech.byproducts?.energies?.forEach(byproduct => {
        console.log(`Processing energy byproduct for node ${nodeId}:`, byproduct);
        if (byproduct.targetNodeId && byproduct.targetNodeId !== nodeId) {
          const targetNodeData = schema.nodes[byproduct.targetNodeId];

          let sourceHandle = 'right-source';
          let targetHandle = 'left-target';

          if (targetNodeData) {
            const sourceY = nodeData.position.y;
            const targetY = targetNodeData.position.y;
            const sourceX = nodeData.position.x;
            const targetX = targetNodeData.position.x;

            // Calculate distances for more natural routing
            const deltaX = targetX - sourceX;
            const deltaY = targetY - sourceY;

            // Smarter routing for energy byproducts - use the most direct path
            const horizontalRatio = Math.abs(deltaX) / (Math.abs(deltaX) + Math.abs(deltaY));
            const verticalRatio = Math.abs(deltaY) / (Math.abs(deltaX) + Math.abs(deltaY));
            const dominanceThreshold = 0.7;

            if (verticalRatio > dominanceThreshold) {
              // Strong vertical flow
              if (deltaY > 0) {
                sourceHandle = 'bottom-source';
                targetHandle = 'top-target';
              } else {
                sourceHandle = 'top-source';
                targetHandle = 'bottom-target';
              }
            } else if (horizontalRatio > dominanceThreshold) {
              // Strong horizontal flow
              if (deltaX > 0) {
                sourceHandle = 'right-source';
                targetHandle = 'left-target';
              } else {
                sourceHandle = 'left-source';
                targetHandle = 'right-target';
              }
            } else {
              // Mixed flow - choose the larger direction
              if (Math.abs(deltaY) > Math.abs(deltaX)) {
                if (deltaY > 0) {
                  sourceHandle = 'bottom-source';
                  targetHandle = 'top-target';
                } else {
                  sourceHandle = 'top-source';
                  targetHandle = 'bottom-target';
                }
              } else {
                if (deltaX > 0) {
                  sourceHandle = 'right-source';
                  targetHandle = 'left-target';
                } else {
                  sourceHandle = 'left-source';
                  targetHandle = 'right-target';
                }
              }
            }
          }

          generatedEdges.push({
            id: `byproduct-energy-${nodeId}-${byproduct.targetNodeId}-${edgeIdCounter++}`,
            source: nodeId,
            sourceHandle: sourceHandle,
            target: byproduct.targetNodeId,
            targetHandle: targetHandle,
            label: byproduct.energy,
            style: {
              stroke: '#f59e0b',
              strokeWidth: 2,
              opacity: 0.85,
              strokeDasharray: '5,5'
            },
            markerEnd: { type: MarkerType.ArrowClosed, color: "#f59e0b" },
            labelStyle: {
              fill: '#787882',
              fontWeight: 400,
              fontSize: 13.5,
            },
            type: 'smoothstep',
            data: {
              type: 'energy-byproduct',
              energy: byproduct.energy
            }
          });
        }
      });
    });

    // Combine all nodes
    const allNodes = [...generatedNodes, ...virtualInputNodes, ...finalOutputNodes];

    console.log('Generated flow from ultra-optimized schema:', {
      nodes: allNodes.length,
      edges: generatedEdges.length,
      virtualInputs: virtualInputNodes.length,
      finalOutputs: finalOutputNodes.length,
      processNodes: generatedNodes.length
    });

    console.log('Generated nodes:', allNodes);
    console.log('Generated edges:', generatedEdges);

    return { nodes: allNodes, edges: generatedEdges };
  };

  // Legacy function for backward compatibility
  const generateFlowFromSchema = (schema: IndustryFlowSchema) => {
    console.log('Generating flow from legacy schema...');

    // Generate process nodes from node data
    const generatedNodes = Object.values(schema.nodeData).map(nodeData => ({
      id: nodeData.id,
      type: nodeData.type,
      position: nodeData.position,
      data: {
        label: nodeData.label,
        technology: nodeData.formData.currentTechnology || nodeData.formData.technology,
        outputs: {} // Will be populated from form data
      },
      style: {} // Default style
    }));

    // Track virtual input nodes and final output nodes to create
    const virtualInputNodes: any[] = [];
    const finalOutputNodes: any[] = [];

    // Generate edges from form data
    const generatedEdges: any[] = [];
    let edgeIdCounter = 0;

    // Process each node's form data to generate edges
    Object.values(schema.nodeData).forEach(nodeData => {
      const nodeId = nodeData.id;

      // Generate standalone input edges from materialInputs where sourceActivity is 'Nil'
      nodeData.formData.materialInputs?.forEach(input => {
        if (input.sourceActivity === 'Nil') {
          // Create virtual input node if it doesn't exist
          const virtualInputId = `virtual-input-${input.material.toLowerCase().replace(/\s+/g, '-')}`;

          // Check if virtual input node already exists in our tracking arrays
          if (!virtualInputNodes.find(n => n.id === virtualInputId) &&
              !generatedNodes.find(n => n.id === virtualInputId)) {
            virtualInputNodes.push({
              id: virtualInputId,
              type: 'input',
              position: { x: nodeData.position.x - 200, y: nodeData.position.y },
              data: { label: input.material },
              style: {
                width: 1,
                height: 1,
                opacity: 0,
                pointerEvents: 'none',
              }
            });
          }

          // Create standalone input edge
          generatedEdges.push({
            id: `input-${input.material.toLowerCase().replace(/\s+/g, '-')}-${nodeId}`,
            source: virtualInputId,
            sourceHandle: 'right-source',
            target: nodeId,
            targetHandle: 'left-target',
            label: input.material,
            style: {
              stroke: "#10B981", // Green color for input edges
              strokeWidth: 3,
              opacity: 0.9,
            },
            markerEnd: { type: MarkerType.ArrowClosed, color: "#10B981" },
            labelStyle: {
              fill: '#787882',
              fontWeight: 400,
              fontSize: 13.5,
            },
            type: 'smoothstep',
            data: {
              type: 'input-material',
              material: input.material,
              sourceActivity: 'Nil',
              technology: 'Nil',
              isInputEdge: true
            }
          });
        }
      });

      // Generate output edges from outputs in form data
      nodeData.formData.outputs?.forEach(output => {
        output.materialOutputs?.forEach(materialOutput => {
          if (materialOutput.connectToNode && materialOutput.connectToNode !== nodeId) {
            // Determine the best connection handles based on node positions
            const sourceNode = nodeData;
            const targetNodeData = Object.values(schema.nodeData).find(n => n.id === materialOutput.connectToNode);

            let sourceHandle = 'right-source';
            let targetHandle = 'left-target';
            const edgeType = 'smoothstep';

            if (sourceNode && targetNodeData) {
              const sourceY = sourceNode.position.y;
              const targetY = targetNodeData.position.y;
              const sourceX = sourceNode.position.x;
              const targetX = targetNodeData.position.x;

              // If target is significantly below source (row-to-row connection)
              if (targetY > sourceY + 100) {
                sourceHandle = 'bottom-source';
                targetHandle = 'top-target';
              }
              // If target is significantly above source
              else if (sourceY > targetY + 100) {
                sourceHandle = 'top-source';
                targetHandle = 'bottom-target';
              }
              // If target is to the left of source (reverse flow)
              else if (targetX < sourceX) {
                sourceHandle = 'left-source';
                targetHandle = 'right-target';
              }
              // Default: left to right flow
              else {
                sourceHandle = 'right-source';
                targetHandle = 'left-target';
              }
            }

            // Create material output edge
            generatedEdges.push({
              id: `e${nodeId}-${materialOutput.connectToNode}-${edgeIdCounter++}`,
              source: nodeId,
              sourceHandle: sourceHandle,
              target: materialOutput.connectToNode,
              targetHandle: targetHandle,
              label: materialOutput.material,
              style: {
                stroke: '#9b87f5',
                strokeWidth: 2,
                opacity: 0.85,
              },
              markerEnd: { type: MarkerType.ArrowClosed, color: "#9b87f5" },
              labelStyle: {
                fill: '#787882',
                fontWeight: 400,
                fontSize: 13.5,
              },
              type: edgeType,
              data: {
                type: 'material-output',
                material: materialOutput.material,
                isFinalOutput: materialOutput.isFinalOutput
              }
            });

            // If this is a final output, create a final output node
            if (materialOutput.isFinalOutput) {
              const finalOutputId = `final-output-${nodeId}-${materialOutput.material.toLowerCase().replace(/\s+/g, '-')}`;

              // Check if final output node already exists
              if (!finalOutputNodes.find(n => n.id === finalOutputId)) {
                // Position final output node below the source node
                finalOutputNodes.push({
                  id: finalOutputId,
                  type: 'finalOutput',
                  position: {
                    x: nodeData.position.x,
                    y: nodeData.position.y + 300
                  },
                  data: {
                    label: materialOutput.material,
                    outputs: {
                      material: materialOutput.material,
                      materialUnit: materialOutput.unit
                    }
                  },
                  style: {}
                });

                // Create edge to final output
                generatedEdges.push({
                  id: `final-${nodeId}-${finalOutputId}`,
                  source: nodeId,
                  sourceHandle: 'bottom-source',
                  target: finalOutputId,
                  targetHandle: 'top-target',
                  label: materialOutput.material,
                  style: {
                    stroke: '#f59e0b',
                    strokeWidth: 3,
                    opacity: 0.9,
                  },
                  markerEnd: { type: MarkerType.ArrowClosed, color: "#f59e0b" },
                  labelStyle: {
                    fill: '#787882',
                    fontWeight: 400,
                    fontSize: 13.5,
                  },
                  type: 'smoothstep',
                  data: {
                    type: 'final-output',
                    material: materialOutput.material
                  }
                });
              }
            }
          }
        });

        output.energyOutputs?.forEach(energyOutput => {
          if (energyOutput.connectToNode && energyOutput.connectToNode !== nodeId) {
            // Determine the best connection handles for energy outputs too
            const sourceNode = nodeData;
            const targetNodeData = Object.values(schema.nodeData).find(n => n.id === energyOutput.connectToNode);

            let sourceHandle = 'right-source';
            let targetHandle = 'left-target';

            if (sourceNode && targetNodeData) {
              const sourceY = sourceNode.position.y;
              const targetY = targetNodeData.position.y;
              const sourceX = sourceNode.position.x;
              const targetX = targetNodeData.position.x;

              // If target is significantly below source (row-to-row connection)
              if (targetY > sourceY + 100) {
                sourceHandle = 'bottom-source';
                targetHandle = 'top-target';
              }
              // If target is significantly above source
              else if (sourceY > targetY + 100) {
                sourceHandle = 'top-source';
                targetHandle = 'bottom-target';
              }
              // If target is to the left of source (reverse flow)
              else if (targetX < sourceX) {
                sourceHandle = 'left-source';
                targetHandle = 'right-target';
              }
            }

            // Create energy output edge
            generatedEdges.push({
              id: `e${nodeId}-${energyOutput.connectToNode}-energy-${edgeIdCounter++}`,
              source: nodeId,
              sourceHandle: sourceHandle,
              target: energyOutput.connectToNode,
              targetHandle: targetHandle,
              label: energyOutput.energy,
              style: {
                stroke: '#f59e0b',
                strokeWidth: 2,
                opacity: 0.85,
              },
              markerEnd: { type: MarkerType.ArrowClosed, color: "#f59e0b" },
              labelStyle: {
                fill: '#787882',
                fontWeight: 400,
                fontSize: 13.5,
              },
              type: 'smoothstep',
              data: {
                type: 'energy-output',
                energy: energyOutput.energy,
                isFinalOutput: energyOutput.isFinalOutput
              }
            });

            // If this is a final energy output, create a final output node
            if (energyOutput.isFinalOutput) {
              const finalOutputId = `final-output-${nodeId}-${energyOutput.energy.toLowerCase().replace(/\s+/g, '-')}`;

              if (!finalOutputNodes.find(n => n.id === finalOutputId)) {
                finalOutputNodes.push({
                  id: finalOutputId,
                  type: 'finalOutput',
                  position: {
                    x: nodeData.position.x,
                    y: nodeData.position.y + 300
                  },
                  data: {
                    label: energyOutput.energy,
                    outputs: {
                      energy: energyOutput.energy,
                      energyUnit: energyOutput.unit
                    }
                  },
                  style: {}
                });

                // Create edge to final output
                generatedEdges.push({
                  id: `final-energy-${nodeId}-${finalOutputId}`,
                  source: nodeId,
                  sourceHandle: 'bottom-source',
                  target: finalOutputId,
                  targetHandle: 'top-target',
                  label: energyOutput.energy,
                  style: {
                    stroke: '#f59e0b',
                    strokeWidth: 3,
                    opacity: 0.9,
                  },
                  markerEnd: { type: MarkerType.ArrowClosed, color: "#f59e0b" },
                  labelStyle: {
                    fill: '#787882',
                    fontWeight: 400,
                    fontSize: 13.5,
                  },
                  type: 'smoothstep',
                  data: {
                    type: 'final-energy-output',
                    energy: energyOutput.energy
                  }
                });
              }
            }
          }
        });
      });

      // Generate byproduct edges with smart routing
      nodeData.formData.materialByProducts?.forEach(byproduct => {
        if (byproduct.connectToNode && byproduct.connectToNode !== nodeId) {
          // For byproducts, we generally want bottom-to-top connections
          // but we can still optimize based on positions
          const sourceNode = nodeData;
          const targetNodeData = Object.values(schema.nodeData).find(n => n.id === byproduct.connectToNode);

          let sourceHandle = 'bottom-source';
          let targetHandle = 'top-target';

          if (sourceNode && targetNodeData) {
            const sourceY = sourceNode.position.y;
            const targetY = targetNodeData.position.y;
            const sourceX = sourceNode.position.x;
            const targetX = targetNodeData.position.x;

            // If target is on the same row or above, use side connections
            if (Math.abs(targetY - sourceY) < 100) {
              if (targetX < sourceX) {
                sourceHandle = 'left-source';
                targetHandle = 'right-target';
              } else {
                sourceHandle = 'right-source';
                targetHandle = 'left-target';
              }
            }
          }

          generatedEdges.push({
            id: `byproduct-${nodeId}-${byproduct.connectToNode}-${edgeIdCounter++}`,
            source: nodeId,
            sourceHandle: sourceHandle,
            target: byproduct.connectToNode,
            targetHandle: targetHandle,
            label: byproduct.byproduct,
            style: {
              stroke: '#8b5cf6',
              strokeWidth: 2,
              opacity: 0.85,
              strokeDasharray: '5,5'
            },
            markerEnd: { type: MarkerType.ArrowClosed, color: "#8b5cf6" },
            labelStyle: {
              fill: '#787882',
              fontWeight: 400,
              fontSize: 13.5,
            },
            type: 'smoothstep',
            data: {
              type: 'material-byproduct',
              byproduct: byproduct.byproduct
            }
          });
        }
      });

      nodeData.formData.energyByProducts?.forEach(byproduct => {
        if (byproduct.connectToNode && byproduct.connectToNode !== nodeId) {
          // Same smart routing for energy byproducts
          const sourceNode = nodeData;
          const targetNodeData = Object.values(schema.nodeData).find(n => n.id === byproduct.connectToNode);

          let sourceHandle = 'bottom-source';
          let targetHandle = 'top-target';

          if (sourceNode && targetNodeData) {
            const sourceY = sourceNode.position.y;
            const targetY = targetNodeData.position.y;
            const sourceX = sourceNode.position.x;
            const targetX = targetNodeData.position.x;

            if (Math.abs(targetY - sourceY) < 100) {
              if (targetX < sourceX) {
                sourceHandle = 'left-source';
                targetHandle = 'right-target';
              } else {
                sourceHandle = 'right-source';
                targetHandle = 'left-target';
              }
            }
          }

          generatedEdges.push({
            id: `byproduct-energy-${nodeId}-${byproduct.connectToNode}-${edgeIdCounter++}`,
            source: nodeId,
            sourceHandle: sourceHandle,
            target: byproduct.connectToNode,
            targetHandle: targetHandle,
            label: byproduct.byproduct,
            style: {
              stroke: '#f59e0b',
              strokeWidth: 2,
              opacity: 0.85,
              strokeDasharray: '5,5'
            },
            markerEnd: { type: MarkerType.ArrowClosed, color: "#f59e0b" },
            labelStyle: {
              fill: '#787882',
              fontWeight: 400,
              fontSize: 13.5,
            },
            type: 'smoothstep',
            data: {
              type: 'energy-byproduct',
              byproduct: byproduct.byproduct
            }
          });
        }
      });
    });

    // Combine all nodes: process nodes + virtual inputs + final outputs
    const allNodes = [...generatedNodes, ...virtualInputNodes, ...finalOutputNodes];

    console.log('Generated process nodes:', generatedNodes.length);
    console.log('Generated virtual input nodes:', virtualInputNodes.length);
    console.log('Generated final output nodes:', finalOutputNodes.length);
    console.log('Total nodes:', allNodes.length);
    console.log('Generated edges:', generatedEdges.length);

    return { nodes: allNodes, edges: generatedEdges };
  };

  // Smart function to detect schema format and call appropriate generator
  const generateFlowFromAnySchema = (schema: any) => {
    console.log('=== DETECTING SCHEMA FORMAT ===');
    console.log('Schema keys:', Object.keys(schema));
    console.log('Schema structure:', schema);

    // Check if it's ultra-optimized format (has nodes as object, may or may not have metadata)
    if (schema.nodes && typeof schema.nodes === 'object' && !Array.isArray(schema.nodes)) {
      // Check if nodes have the ultra-optimized structure (activity, technologies, position)
      const firstNodeKey = Object.keys(schema.nodes)[0];
      const firstNode = schema.nodes[firstNodeKey];

      if (firstNode && firstNode.activity && firstNode.technologies && firstNode.position) {
        console.log('Detected: Ultra-optimized schema format');
        console.log('Ultra-optimized nodes:', Object.keys(schema.nodes));
        console.log('Ultra-optimized metadata:', schema.metadata || 'No metadata');

        try {
          return generateFlowFromUltraOptimizedSchema(schema as UltraOptimizedFlowSchema);
        } catch (error) {
          console.error('Error generating from ultra-optimized schema:', error);
          console.log('Falling back to legacy format...');
          // Fall through to legacy format
        }
      }
    }

    // Check if it's legacy format (has nodeData)
    if (schema.nodeData && typeof schema.nodeData === 'object') {
      console.log('Detected: Legacy schema format');
      console.log('Legacy nodeData keys:', Object.keys(schema.nodeData));
      return generateFlowFromSchema(schema as IndustryFlowSchema);
    }

    // Check if it's a simplified format that needs conversion
    if (schema.nodes && Array.isArray(schema.nodes)) {
      console.log('Detected: Array-based nodes format, converting to legacy...');
      // Convert array-based nodes to legacy nodeData format
      const nodeData: Record<string, any> = {};
      schema.nodes.forEach((node: any) => {
        nodeData[node.id] = node;
      });

      const legacySchema: IndustryFlowSchema = {
        name: schema.name || 'Unknown Flow',
        flowType: schema.flowType || 'inventory',
        createdAt: schema.createdAt || new Date().toISOString(),
        updatedAt: schema.updatedAt || new Date().toISOString(),
        nodeData: nodeData,
        industryId: schema.industryId
      };
      return generateFlowFromSchema(legacySchema);
    }

    // If neither format is detected, try to convert to legacy format
    console.warn('Unknown schema format, attempting legacy conversion');
    const legacySchema: IndustryFlowSchema = {
      name: schema.name || 'Unknown Flow',
      flowType: schema.flowType || 'inventory',
      createdAt: schema.createdAt || new Date().toISOString(),
      updatedAt: schema.updatedAt || new Date().toISOString(),
      nodeData: schema.nodeData || {},
      industryId: schema.industryId
    };
    return generateFlowFromSchema(legacySchema);
  };

  // Function to restore form data from ultra-optimized schema
  const restoreFormDataFromUltraOptimizedSchema = (schema: UltraOptimizedFlowSchema) => {
    console.log('Restoring form data from ultra-optimized schema...');
    console.log('Schema structure:', schema);
    const restoredFormData: Record<string, any> = {};

    try {
      Object.entries(schema.nodes).forEach(([nodeId, nodeData]) => {
        console.log(`Processing node ${nodeId}:`, nodeData);

        const currentTech = nodeData.technologies?.[0]; // Get first technology
        if (!currentTech) {
          console.warn(`No technology found for node ${nodeId}`);
          return;
        }

        console.log(`Processing technology for node ${nodeId}:`, currentTech);

        // Convert ultra-optimized data back to legacy form data structure
        restoredFormData[nodeId] = {
          formData: {
            activity: nodeData.activity,
            currentTechnology: currentTech.name,
            technology: currentTech.name,
            startYear: currentTech.startYear?.toString() || '',
            endYear: currentTech.endYear?.toString() || '',
            technologyEmission: currentTech.emission?.toString() || '',

            // Convert inputs back to legacy format
            materialInputs: (currentTech.inputs?.materials || []).map((input: any) => ({
              id: input.id,
              material: input.material,
              unit: input.unit,
              quantity: input.quantity?.toString() || '1',
              cost: input.cost?.toString() || '0',
              specificMaterialCost: input.specificMaterialCost?.toString() || '0',
              sourceActivity: input.sourceNodeId || 'Nil',
              technology: input.sourceTechnology || 'Nil',
              lowerBound: input.lowerBound?.toString() || '',
              upperBound: input.upperBound?.toString() || ''
            })),

            energyInputs: (currentTech.inputs?.energies || []).map((input: any) => ({
              id: input.id,
              source: input.energy, // Map energy to source for legacy compatibility
              unit: input.unit,
              quantity: input.quantity?.toString() || '1',
              cost: input.cost?.toString() || '0',
              sec: input.specificEnergyCost?.toString() || '0', // Map to legacy field name
              sourceActivity: input.sourceNodeId || 'Nil',
              technology: input.sourceTechnology || 'Nil',
              lowerBound: input.lowerBound?.toString() || '',
              upperBound: input.upperBound?.toString() || ''
            })),

            emissions: (currentTech.inputs?.emissions || []).map((emission: any) => ({
              id: emission.id,
              emission: emission.emission,
              unit: emission.unit,
              emissionFactor: emission.emissionFactor?.toString() || '0',
              cost: emission.cost?.toString() || '0'
            })),

            // Convert outputs back to legacy format
            materialOutputs: (currentTech.outputs?.materials || []).map((output: any) => ({
              id: output.id,
              material: output.material,
              unit: output.unit,
              smc: output.specificMaterialCost?.toString() || '0', // Convert to legacy field name
              quantity: output.quantity?.toString() || '1',
              isFinalOutput: output.isFinalOutput || false,
              connectToNode: output.targetNodeId || '',
              destinationTechnology: output.targetTechnology || ''
            })),

            energyOutputs: (currentTech.outputs?.energies || []).map((output: any) => ({
              id: output.id,
              energy: output.energy,
              unit: output.unit,
              sec: output.specificEnergyCost?.toString() || '0', // Convert to legacy field name
              quantity: output.quantity?.toString() || '1',
              isFinalOutput: output.isFinalOutput || false,
              connectToNode: output.targetNodeId || '',
              destinationTechnology: output.targetTechnology || ''
            })),

            // Convert byproducts back to legacy format
            materialByProducts: (currentTech.byproducts?.materials || []).map((byproduct: any) => ({
              id: byproduct.id,
              byproduct: byproduct.material,
              unit: byproduct.unit,
              byproductPerPrimaryOutput: byproduct.quantity?.toString() || '1',
              connectToNode: byproduct.targetNodeId || ''
            })),

            energyByProducts: (currentTech.byproducts?.energies || []).map((byproduct: any) => ({
              id: byproduct.id,
              byproduct: byproduct.energy,
              unit: byproduct.unit,
              byproductPerPrimaryOutput: byproduct.quantity?.toString() || '1',
              connectToNode: byproduct.targetNodeId || ''
            })),

            // Convert financial data back to standardized format
            financial: {
              capacity: currentTech.financial?.capacity?.toString() || '0',
              capacityUnit: currentTech.financial?.capacityUnit || 'Units/day',
              // Use standardized capitalCost field
              capitalCost: currentTech.financial?.capitalCost?.toString() || '0',
              operatingMaintenanceCost: currentTech.financial?.operatingMaintenanceCost?.toString() || '0'
            }
          },
          // Create outputs structure in legacy format
          outputs: [{
            id: `output-${nodeId}`,
            targetNode: '',
            outputTechnology: currentTech.name,
            // Convert energy outputs to legacy format
            energyOutputs: (currentTech.outputs?.energies || []).map((output: any) => ({
              id: output.id,
              energy: output.energy,
              unit: output.unit,
              sec: output.specificEnergyCost?.toString() || '0', // Convert to legacy field name
              final: output.isFinalOutput || false,
              connect: output.targetNodeId || '', // Convert to legacy field name
              qty: output.quantity?.toString() || '1',
              qtyUnit: output.unit,
              destinationTechnology: output.targetTechnology || ''
            })),
            // Convert material outputs to legacy format
            matOutputs: (currentTech.outputs?.materials || []).map((output: any) => ({
              id: output.id,
              material: output.material,
              unit: output.unit,
              smc: output.specificMaterialCost?.toString() || '0', // Convert to legacy field name
              final: output.isFinalOutput || false,
              connect: output.targetNodeId || '', // Convert to legacy field name
              qty: output.quantity?.toString() || '1',
              qtyUnit: output.unit,
              destinationTechnology: output.targetTechnology || ''
            }))
          }],
          technologies: [currentTech.name],
          technologyFormData: {
            [currentTech.name]: {
              // Add technology emission field
              technologyEmission: currentTech.emission?.toString() || '',
              // Convert material inputs to legacy format for UI compatibility
              materialInputs: (currentTech.inputs?.materials || []).map((input: any) => ({
                id: input.id,
                material: input.material,
                unit: input.unit,
                cost: input.cost?.toString() || '0',
                smc: input.specificMaterialCost?.toString() || '0', // Convert to legacy field name
                sourceActivity: input.sourceNodeId || 'Nil',
                technology: input.sourceTechnology || 'Nil',
                lowerBound: input.lowerBound?.toString() || '',
                upperBound: input.upperBound?.toString() || ''
              })),
              // Convert energy inputs to legacy format for UI compatibility
              energyInputs: (currentTech.inputs?.energies || []).map((input: any) => ({
                id: input.id,
                source: input.energy,
                unit: input.unit,
                cost: input.cost?.toString() || '0',
                sec: input.specificEnergyCost?.toString() || '0', // Convert to legacy field name
                sourceActivity: input.sourceNodeId || 'Nil',
                technology: input.sourceTechnology || 'Nil',
                lowerBound: input.lowerBound?.toString() || '',
                upperBound: input.upperBound?.toString() || ''
              })),
              // Convert emissions to legacy format for UI compatibility
              emissions: (currentTech.inputs?.emissions || []).map((emission: any) => ({
                id: emission.id,
                source: emission.emission,
                factor: emission.emissionFactor?.toString() || '0', // Convert to legacy field name
                unit: emission.unit
              })),
              // Convert material outputs to legacy format for UI compatibility
              materialOutputs: (currentTech.outputs?.materials || []).map((output: any) => ({
                id: output.id,
                material: output.material,
                unit: output.unit,
                smc: output.specificMaterialCost?.toString() || '0', // Convert to legacy field name
                final: output.isFinalOutput || false,
                connect: output.targetNodeId || '', // Convert to legacy field name
                qty: output.quantity?.toString() || '1',
                qtyUnit: output.unit,
                destinationTechnology: output.targetTechnology || ''
              })),
              // Convert energy outputs to legacy format for UI compatibility
              energyOutputs: (currentTech.outputs?.energies || []).map((output: any) => ({
                id: output.id,
                energy: output.energy,
                unit: output.unit,
                sec: output.specificEnergyCost?.toString() || '0', // Convert to legacy field name
                final: output.isFinalOutput || false,
                connect: output.targetNodeId || '', // Convert to legacy field name
                qty: output.quantity?.toString() || '1',
                qtyUnit: output.unit,
                destinationTechnology: output.targetTechnology || ''
              })),
              materialByProducts: currentTech.byproducts?.materials || [],
              energyByProducts: currentTech.byproducts?.energies || [],
              // Convert financial data to standardized format for UI compatibility
              financial: {
                capacity: currentTech.financial?.capacity?.toString() || '0',
                capacityUnit: currentTech.financial?.capacityUnit || 'Units/day',
                // Use standardized capitalCost field
                capitalCost: currentTech.financial?.capitalCost?.toString() || '0',
                operatingMaintenanceCost: currentTech.financial?.operatingMaintenanceCost?.toString() || '0'
              }
            }
          },
          completedAt: new Date().toISOString()
        };
      });

      console.log('Successfully restored form data:', restoredFormData);
      return restoredFormData;
    } catch (error) {
      console.error('Error restoring form data from ultra-optimized schema:', error);
      return {};
    }
  };

  // Comprehensive restore function for database integration
  const restoreFlowFromDatabase = (flowSchema: any) => {
    try {
      console.log('Restoring flow from schema:', flowSchema);

      // Generate nodes and edges using smart detection
      const { nodes: generatedNodes, edges: generatedEdges } = generateFlowFromAnySchema(flowSchema);

      // Restore nodes and edges
      setNodes(generatedNodes);
      setEdges(generatedEdges);

      // Restore form data based on schema format
      let restoredFormData: Record<string, any> = {};

      if (flowSchema.nodes && typeof flowSchema.nodes === 'object' && !Array.isArray(flowSchema.nodes)) {
        // Check if it's ultra-optimized format by examining node structure
        const firstNodeKey = Object.keys(flowSchema.nodes)[0];
        const firstNode = flowSchema.nodes[firstNodeKey];

        if (firstNode && firstNode.activity && firstNode.technologies && firstNode.position) {
          // Ultra-optimized format
          console.log('Restoring form data from ultra-optimized schema');
          restoredFormData = restoreFormDataFromUltraOptimizedSchema(flowSchema as UltraOptimizedFlowSchema);
        }
      } else if (flowSchema.nodeData && typeof flowSchema.nodeData === 'object') {
        // Legacy format
        console.log('Restoring form data from legacy schema');
        Object.values(flowSchema.nodeData).forEach((nodeData: any) => {
        // Extract outputs from form data and convert back to legacy format for compatibility
        const outputs = (nodeData.formData.outputs || []).map(output => ({
          id: output.id,
          targetNode: output.targetNode,
          outputTechnology: output.outputTechnology,
          energyOutputs: output.energyOutputs.map(energyOut => ({
            id: energyOut.id,
            energy: energyOut.energy,
            unit: energyOut.unit,
            sec: energyOut.specificEnergyCost, // Convert back to legacy field name
            final: energyOut.isFinalOutput, // Convert back to legacy field name
            connect: energyOut.connectToNode, // Convert back to legacy field name
            qty: energyOut.quantity, // Convert back to legacy field name
            qtyUnit: energyOut.quantityUnit, // Convert back to legacy field name
            destinationTechnology: energyOut.destinationTechnology
          })),
          matOutputs: output.materialOutputs.map(materialOut => ({
            id: materialOut.id,
            material: materialOut.material,
            unit: materialOut.unit,
            smc: materialOut.specificMaterialCost, // Convert back to legacy field name
            final: materialOut.isFinalOutput, // Convert back to legacy field name
            connect: materialOut.connectToNode, // Convert back to legacy field name
            qty: materialOut.quantity, // Convert back to legacy field name
            qtyUnit: materialOut.quantityUnit, // Convert back to legacy field name
            destinationTechnology: materialOut.destinationTechnology
          }))
        }));

        // Convert technologies back to legacy format
        const technologies = nodeData.technologies.map(tech => tech.name);
        const technologyFormData: Record<string, any> = {};
        nodeData.technologies.forEach(tech => {
          technologyFormData[tech.name] = tech.formData;
        });

        // Convert form data back to legacy format for compatibility
        const legacyFormData = {
          ...nodeData.formData,
          technology: nodeData.formData.currentTechnology, // Convert back to legacy field name
          // Convert arrays back to legacy field names
          materialInputs: nodeData.formData.materialInputs?.map(input => ({
            ...input,
            smc: input.specificMaterialCost // Convert back to legacy field name
          })),
          energyInputs: nodeData.formData.energyInputs?.map(input => ({
            ...input,
            sec: input.specificEnergyCost // Convert back to legacy field name
          })),
          emissions: nodeData.formData.emissions?.map(emission => ({
            ...emission,
            ef: emission.emissionFactor // Convert back to legacy field name
          })),
          materialByProducts: nodeData.formData.materialByProducts?.map(byproduct => ({
            ...byproduct,
            bppo: byproduct.byproductPerPrimaryOutput, // Convert back to legacy field name
            connect: byproduct.connectToNode, // Convert back to legacy field name
            replaced: byproduct.replacedMaterial, // Convert back to legacy field name
            techEmissionFactor: byproduct.technologyEmissionFactor
          })),
          energyByProducts: nodeData.formData.energyByProducts?.map(byproduct => ({
            ...byproduct,
            bppo: byproduct.byproductPerPrimaryOutput, // Convert back to legacy field name
            connect: byproduct.connectToNode, // Convert back to legacy field name
            replaced: byproduct.replacedEnergy // Convert back to legacy field name
          })),
          financial: {
            ...nodeData.formData.financial,
            omCost: nodeData.formData.financial.operatingAndMaintenanceCost // Convert back to legacy field name
          }
        };

          restoredFormData[nodeData.id] = {
            formData: legacyFormData,
            outputs: outputs,
            technologies: technologies,
            technologyFormData: technologyFormData,
            completedAt: nodeData.completedAt
          };
        });
      }

      // Restore form data
      setNodeFormData(restoredFormData);

      // Restore meta information
      setScenarioName(flowSchema.name);
      setIsScenarioMode(flowSchema.flowType === 'scenario');

      // Generate final outputs from restored form data
      setTimeout(() => {
        generateFinalOutputsFromFormData(generatedNodes);
      }, 100);

      toast({
        title: 'Flow Restored',
        description: `Successfully restored "${flowSchema.name}" from simplified schema.`
      });

      console.log('Flow restoration completed from simplified schema');
      console.log('Restored nodes:', generatedNodes.length);
      console.log('Restored edges:', generatedEdges.length);
      console.log('Restored form data entries:', Object.keys(restoredFormData).length);
    } catch (error) {
      console.error('Error restoring flow from simplified schema:', error);
      toast({
        title: 'Restore Error',
        description: 'Could not restore flow from simplified schema.',
        variant: 'destructive'
      });
    }
  };

  // Function to load a flow diagram and return the data without updating state
  const loadFlowDiagramAndReturnData = async (flowDiagramId: string) => {
    try {
      console.log('=== LOADING FLOW DIAGRAM FOR DATA EXTRACTION ===');
      console.log('Flow diagram ID to load:', flowDiagramId);

      let flowDiagram;

      // Try different methods to find the flow diagram
      if (flowDiagramId && !flowDiagramId.startsWith('index-')) {
        flowDiagram = flowDiagrams.find((fd, index) => {
          const flowId = fd.flow_diagram_uuid || fd.uuid || `api-flow-${index}`;
          return flowId === flowDiagramId;
        });
      }

      if (!flowDiagram && flowDiagramId.startsWith('index-')) {
        const index = parseInt(flowDiagramId.replace('index-', ''));
        if (index >= 0 && index < flowDiagrams.length) {
          flowDiagram = flowDiagrams[index];
        }
      }

      if (!flowDiagram) {
        flowDiagram = flowDiagrams.find(fd => fd.name === flowDiagramId);
      }

      if (!flowDiagram) {
        console.error('Flow diagram not found with ID:', flowDiagramId);
        return null;
      }

      const flowSchema: IndustryFlowSchema = flowDiagram.flow_diagram;

      if (!flowSchema) {
        throw new Error('Flow diagram does not contain valid flow schema data');
      }

      // Extract form data from the flow schema without updating state
      const extractedFormData: Record<string, any> = {};

      console.log('=== EXTRACTING FORM DATA FROM FLOW SCHEMA ===');
      console.log('Flow schema structure:', {
        hasNodeData: !!flowSchema.nodeData,
        hasNodes: !!flowSchema.nodes,
        nodeDataKeys: flowSchema.nodeData ? Object.keys(flowSchema.nodeData) : [],
        nodesKeys: flowSchema.nodes ? Object.keys(flowSchema.nodes) : [],
        schemaKeys: Object.keys(flowSchema)
      });

      // Handle different schema formats
      if (flowSchema.nodeData && Object.keys(flowSchema.nodeData).length > 0) {
        console.log('Using legacy nodeData format for form data extraction');
        Object.values(flowSchema.nodeData).forEach(nodeData => {
        // Convert clean node data back to the expected nodeFormData format
        const outputs = (nodeData.formData.outputs || []).map(output => ({
          id: output.id,
          targetNode: output.targetNode,
          outputTechnology: output.outputTechnology,
          energyOutputs: output.energyOutputs.map(energyOut => ({
            id: energyOut.id,
            energy: energyOut.energy,
            unit: energyOut.unit,
            sec: energyOut.specificEnergyCost,
            final: energyOut.isFinalOutput,
            connect: energyOut.connectToNode,
            qty: energyOut.quantity,
            qtyUnit: energyOut.quantityUnit,
            destinationTechnology: energyOut.destinationTechnology
          })),
          matOutputs: output.materialOutputs.map(materialOut => ({
            id: materialOut.id,
            material: materialOut.material,
            unit: materialOut.unit,
            smc: materialOut.specificMaterialCost,
            final: materialOut.isFinalOutput,
            connect: materialOut.connectToNode,
            qty: materialOut.quantity,
            qtyUnit: materialOut.quantityUnit,
            destinationTechnology: materialOut.destinationTechnology
          }))
        }));

        // Convert technologies back to legacy format
        const technologies = nodeData.technologies.map(tech => tech.name);
        const technologyFormData: Record<string, any> = {};
        nodeData.technologies.forEach(tech => {
          technologyFormData[tech.name] = tech.formData;
        });

        // Convert form data back to legacy format for compatibility
        const legacyFormData = {
          ...nodeData.formData,
          technology: nodeData.formData.currentTechnology,
          materialInputs: nodeData.formData.materialInputs?.map(input => ({
            ...input,
            smc: input.specificMaterialCost
          })),
          energyInputs: nodeData.formData.energyInputs?.map(input => ({
            ...input,
            sec: input.specificEnergyCost
          })),
          emissions: nodeData.formData.emissions?.map(emission => ({
            ...emission,
            ef: emission.emissionFactor
          })),
          materialByProducts: nodeData.formData.materialByProducts?.map(byproduct => ({
            ...byproduct,
            bppo: byproduct.byproductPerPrimaryOutput,
            connect: byproduct.connectToNode,
            replaced: byproduct.replacedMaterial,
            techEmissionFactor: byproduct.technologyEmissionFactor
          })),
          energyByProducts: nodeData.formData.energyByProducts?.map(byproduct => ({
            ...byproduct,
            bppo: byproduct.byproductPerPrimaryOutput,
            connect: byproduct.connectToNode,
            replaced: byproduct.replacedEnergy
          })),
          financial: {
            ...nodeData.formData.financial,
            omCost: nodeData.formData.financial.operatingAndMaintenanceCost
          }
        };

          extractedFormData[nodeData.id] = {
            formData: legacyFormData,
            outputs: outputs,
            technologies: technologies,
            technologyFormData: technologyFormData,
            completedAt: nodeData.completedAt
          };
        });
      } else if (flowSchema.nodes && Object.keys(flowSchema.nodes).length > 0) {
        console.log('Using ultra-optimized nodes format for form data extraction');

        // Extract form data from ultra-optimized schema
        Object.entries(flowSchema.nodes).forEach(([nodeId, nodeData]: [string, any]) => {
          console.log(`Extracting data for node ${nodeId}:`, nodeData);

          if (!nodeData.technologies || nodeData.technologies.length === 0) {
            console.warn(`Node ${nodeId} has no technologies, skipping`);
            return;
          }

          // Get the first technology (or primary technology)
          const primaryTech = nodeData.technologies[0];
          console.log(`Primary technology for node ${nodeId}:`, primaryTech);

          // Convert ultra-optimized format to legacy format for compatibility
          const legacyFormData = {
            activity: nodeData.activity,
            technology: primaryTech.name,
            currentTechnology: primaryTech.name,
            startYear: primaryTech.startYear,
            endYear: primaryTech.endYear,
            materialInputs: primaryTech.inputs?.materials || [],
            energyInputs: primaryTech.inputs?.energies || [],
            emissions: primaryTech.inputs?.emissions || [],
            materialByProducts: primaryTech.byproducts?.materials || [],
            energyByProducts: primaryTech.byproducts?.energies || [],
            financial: primaryTech.financial || {}
          };

          // Convert outputs from ultra-optimized format to legacy format
          const outputs = [{
            id: `output-${nodeId}`,
            targetNode: '',
            outputTechnology: primaryTech.name,
            // Convert energy outputs to legacy format
            energyOutputs: (primaryTech.outputs?.energies || []).map((output: any) => ({
              id: output.id,
              energy: output.energy,
              unit: output.unit,
              sec: output.specificEnergyCost?.toString() || '0', // Convert to legacy field name
              final: output.isFinalOutput || false,
              connect: output.targetNodeId || '', // Convert to legacy field name
              qty: output.quantity?.toString() || '1',
              qtyUnit: output.unit,
              destinationTechnology: output.targetTechnology || ''
            })),
            // Convert material outputs to legacy format
            matOutputs: (primaryTech.outputs?.materials || []).map((output: any) => ({
              id: output.id,
              material: output.material,
              unit: output.unit,
              smc: output.specificMaterialCost?.toString() || '0', // Convert to legacy field name
              final: output.isFinalOutput || false,
              connect: output.targetNodeId || '', // Convert to legacy field name
              qty: output.quantity?.toString() || '1',
              qtyUnit: output.unit,
              destinationTechnology: output.targetTechnology || ''
            }))
          }];

          // Convert technologies
          const technologies = nodeData.technologies.map((tech: any) => tech.name);

          // Convert technology form data
          const technologyFormData: Record<string, any> = {};
          nodeData.technologies.forEach((tech: any) => {
            technologyFormData[tech.name] = {
              // Add technology emission field
              technologyEmission: tech.emission?.toString() || '',
              // Convert material inputs to legacy format for UI compatibility
              materialInputs: (tech.inputs?.materials || []).map((input: any) => ({
                id: input.id,
                material: input.material,
                unit: input.unit,
                cost: input.cost?.toString() || '0',
                smc: input.specificMaterialCost?.toString() || '0', // Convert to legacy field name
                sourceActivity: input.sourceNodeId || 'Nil',
                technology: input.sourceTechnology || 'Nil',
                lowerBound: input.lowerBound?.toString() || '',
                upperBound: input.upperBound?.toString() || ''
              })),
              // Convert energy inputs to legacy format for UI compatibility
              energyInputs: (tech.inputs?.energies || []).map((input: any) => ({
                id: input.id,
                source: input.energy,
                unit: input.unit,
                cost: input.cost?.toString() || '0',
                sec: input.specificEnergyCost?.toString() || '0', // Convert to legacy field name
                sourceActivity: input.sourceNodeId || 'Nil',
                technology: input.sourceTechnology || 'Nil',
                lowerBound: input.lowerBound?.toString() || '',
                upperBound: input.upperBound?.toString() || ''
              })),
              // Convert emissions to legacy format for UI compatibility
              emissions: (tech.inputs?.emissions || []).map((emission: any) => ({
                id: emission.id,
                source: emission.emission,
                factor: emission.emissionFactor?.toString() || '0', // Convert to legacy field name
                unit: emission.unit
              })),
              // Convert material outputs to legacy format for UI compatibility
              materialOutputs: (tech.outputs?.materials || []).map((output: any) => ({
                id: output.id,
                material: output.material,
                unit: output.unit,
                smc: output.specificMaterialCost?.toString() || '0', // Convert to legacy field name
                final: output.isFinalOutput || false,
                connect: output.targetNodeId || '', // Convert to legacy field name
                qty: output.quantity?.toString() || '1',
                qtyUnit: output.unit,
                destinationTechnology: output.targetTechnology || ''
              })),
              // Convert energy outputs to legacy format for UI compatibility
              energyOutputs: (tech.outputs?.energies || []).map((output: any) => ({
                id: output.id,
                energy: output.energy,
                unit: output.unit,
                sec: output.specificEnergyCost?.toString() || '0', // Convert to legacy field name
                final: output.isFinalOutput || false,
                connect: output.targetNodeId || '', // Convert to legacy field name
                qty: output.quantity?.toString() || '1',
                qtyUnit: output.unit,
                destinationTechnology: output.targetTechnology || ''
              })),
              materialByProducts: tech.byproducts?.materials || [],
              energyByProducts: tech.byproducts?.energies || [],
              // Convert financial data to legacy format for UI compatibility
              financial: {
                capacity: tech.financial?.capacity?.toString() || '0',
                capacityUnit: tech.financial?.capacityUnit || 'Units/day',
                // Fix: Map capitalCost back to capitalCostUnit field (which stores the capital cost per unit capacity value)
                capitalCostUnit: tech.financial?.capitalCost?.toString() || '0',
                omCost: tech.financial?.operatingMaintenanceCost?.toString() || '0' // Convert to legacy field name
              }
            };
          });

          extractedFormData[nodeId] = {
            formData: legacyFormData,
            outputs: outputs,
            technologies: technologies,
            technologyFormData: technologyFormData,
            completedAt: new Date().toISOString()
          };

          console.log(`Extracted form data for node ${nodeId}:`, extractedFormData[nodeId]);
        });
      } else {
        console.warn('No nodeData or nodes found in flow schema, cannot extract form data');
      }

      console.log('=== FINAL EXTRACTED FORM DATA ===');
      console.log('Extracted form data keys:', Object.keys(extractedFormData));
      console.log('Extracted form data:', extractedFormData);
      console.log('=====================================');

      // Don't load the flow diagram into the UI - we only want to extract the data
      // await loadFlowDiagram(flowDiagramId);

      return {
        formData: extractedFormData,
        flowSchema: flowSchema,
        flowDiagram: flowDiagram
      };

    } catch (error) {
      console.error('Error loading flow diagram for data extraction:', error);
      return null;
    }
  };

  // Test function to restore the most recent saved flow
  // Function to load a flow diagram from API
  const loadFlowDiagram = async (flowDiagramId: string) => {
    try {
      console.log('=== LOADING FLOW DIAGRAM ===');
      console.log('Flow diagram ID to load:', flowDiagramId);
      console.log('Current mode - isScenarioMode:', isScenarioMode);

      // Prevent loading flow diagrams when in scenario mode
      // Scenarios should only be modified through the dual-tab editor
      if (isScenarioMode) {
        console.log('Cannot load flow diagram in scenario mode');
        toast({
          title: 'Cannot Load in Scenario Mode',
          description: 'Please exit scenario mode first to load a different flow diagram.',
          variant: 'destructive'
        });
        return;
      }

      console.log('Available flow diagrams:', flowDiagrams.map(fd => ({
        uuid: fd.uuid,
        flowId: fd.uuid,
        name: fd.name,
        tag: fd.tag
      })));

      let flowDiagram;

      // Try different methods to find the flow diagram
      console.log('Searching for flow diagram with ID:', flowDiagramId);

      // Method 1: Try to find by UUID (if available)
      if (flowDiagramId && !flowDiagramId.startsWith('index-')) {
        flowDiagram = flowDiagrams.find(fd => {
          const flowId = fd.uuid;
          const hasMatch = flowId === flowDiagramId;
          console.log(`ID comparison: "${flowId || 'No ID'}" === "${flowDiagramId}" = ${hasMatch}`);
          return hasMatch;
        });
        console.log('Found by UUID:', flowDiagram ? `${flowDiagram.name}` : 'Not found');
      }

      // Method 2: Try index-based lookup (primary method for API without UUIDs)
      if (!flowDiagram && flowDiagramId.startsWith('index-')) {
        const index = parseInt(flowDiagramId.replace('index-', ''));
        console.log(`Trying index-based lookup: index ${index}`);
        if (index >= 0 && index < flowDiagrams.length) {
          flowDiagram = flowDiagrams[index];
          console.log('Found by index:', flowDiagram ? `${flowDiagram.name} (index ${index})` : 'Not found');
        }
      }

      // Method 3: Try to find by name (fallback)
      if (!flowDiagram) {
        console.log('Trying name-based lookup...');
        flowDiagram = flowDiagrams.find(fd => fd.name === flowDiagramId);
        console.log('Found by name:', flowDiagram ? `${flowDiagram.name}` : 'Not found');
      }

      if (!flowDiagram) {
        console.error('Flow diagram not found with ID:', flowDiagramId);
        console.error('Available flow diagram IDs:', flowDiagrams.map(fd => fd.uuid));
        toast({
          title: 'Flow Diagram Not Found',
          description: 'The selected flow diagram could not be found.',
          variant: 'destructive'
        });
        return;
      }

      console.log('Found flow diagram:', flowDiagram);

      const flowSchema = flowDiagram.flow_diagram; // Don't cast to specific type - let smart detection handle it

      console.log('=== LOADING FLOW DIAGRAM FROM API ===');
      console.log('Flow diagram ID:', flowDiagramId);
      console.log('Flow diagram UUID:', flowDiagram.uuid);
      console.log('Flow diagram flowId:', flowDiagram.uuid);
      console.log('Flow diagram tag:', flowDiagram.tag);
      console.log('Flow diagram name:', flowDiagram.name);
      console.log('Flow schema type:', typeof flowSchema);
      console.log('Flow schema keys:', flowSchema ? Object.keys(flowSchema) : 'null');
      console.log('Flow schema:', flowSchema);

      // Validate that the flow schema exists and has required data
      if (!flowSchema) {
        throw new Error('Flow diagram does not contain valid flow schema data');
      }

      // Use the existing restore function with smart detection
      restoreFlowFromDatabase(flowSchema);

      // Set current flow diagram identifier for future updates
      const diagramIdentifier = flowDiagram.uuid || flowDiagramId;
      setCurrentFlowDiagramUuid(diagramIdentifier);

      // Update scenario name with the flow diagram name
      const displayName = flowDiagram.name || flowSchema.name || flowDiagram.tag || flowDiagram.flow_type || 'Unnamed Flow';
      setScenarioName(displayName);

      toast({
        title: 'Flow Diagram Loaded',
        description: `Successfully loaded "${displayName}".`
      });
    } catch (error) {
      console.error('Error loading flow diagram:', error);
      console.error('Error details:', {
        flowDiagramId,
        availableFlowDiagrams: flowDiagrams.map(fd => ({
          uuid: fd.uuid,
          flowId: fd.uuid,
          name: fd.name,
          tag: fd.tag
        })),
        errorMessage: error.message,
        errorStack: error.stack
      });

      toast({
        title: 'Load Error',
        description: `Could not load the selected flow diagram: ${error.message || 'Unknown error'}`,
        variant: 'destructive'
      });
    }
  };

  // Function to refresh flow diagrams from API
  const refreshFlowDiagrams = async () => {
    setIsLoadingFlowDiagrams(true);
    try {
      // Get sector UUID from industry ID
      const sectorUuid = await getSectorUuidFromIndustryId(industryId || '');

      // Fetch flow diagrams for this sector
      const diagrams = await fetchFlowDiagrams(sectorUuid, { toast });
      setFlowDiagrams(diagrams);

      console.log('Refreshed flow diagrams:', diagrams);

      toast({
        title: 'Flow Diagrams Refreshed',
        description: `Loaded ${diagrams.length} flow diagrams from the server.`
      });
    } catch (error) {
      console.error('Error refreshing flow diagrams:', error);
      toast({
        title: "Error Refreshing Flow Diagrams",
        description: "Could not refresh flow diagrams from server.",
        variant: "destructive"
      });
    } finally {
      setIsLoadingFlowDiagrams(false);
    }
  };



  const handleLoadFlow = (flow: SavedFlow) => {
    // If in subflow mode, exit it first
    if (isSubflowMode) {
      exitSubflowMode();
    }

    // If in scenario mode, ask if user wants to return to inventory
    if (isScenarioMode) {
      // For now, we'll just return to inventory mode
      setIsScenarioMode(false);
    }

    setScenarioName(flow.title);
    setNodes(flow.nodes);
    setEdges(flow.edges);

    // Restore form data if available (this ensures SMC values are preserved)
    if (flow.nodeFormData) {
      setNodeFormData(flow.nodeFormData);
      console.log('Restored form data from saved flow:', Object.keys(flow.nodeFormData));
    } else {
      console.warn('No form data found in saved flow - SMC values may not be preserved');
    }

    // Update inventory nodes/edges if not in scenario mode
    if (!isScenarioMode) {
      setInventoryNodes(flow.nodes);
      setInventoryEdges(flow.edges);
    }

    toast({
      title: 'Flow Loaded',
      description: `Loaded "${flow.title}" from your inventory.`
    });
  };

  // Function to exit scenario mode and return to inventory
  const exitScenarioMode = () => {
    if (isScenarioMode) {
      setIsScenarioMode(false);
      setNodes(inventoryNodes);
      setEdges(inventoryEdges);
      toast({
        title: 'Returned to Inventory',
        description: 'You are now back to the main inventory view.'
      });
    }
  };

  // Function to exit subflow mode and restore the original flow
  const exitSubflowMode = () => {
    if (isSubflowMode && originalNodes.length > 0) {
      // Restore the original nodes and edges
      setNodes(originalNodes);
      setEdges(originalEdges);
      
      // Reset subflow state
      setIsSubflowMode(false);
      setActiveNodeId(null);
      setSubflowType(null);
      setOriginalNodes([]);
      setOriginalEdges([]);
      
      toast({
        title: 'Exited Subflow Mode',
        description: 'Returned to main flow view'
      });
    }
  };

  const handleAddFirstNode = () => {
    setShowNodeCreationDialog(true);
  };

  const handlePlusClick = () => {
    setShowNodeCreationDialog(true);
  };

  const handleNodeCreate = (activityName: string) => {
    console.log('IndustryFlow: handleNodeCreate called with:', activityName);
    console.log('Current nodes length:', nodes.length);

    const lastNode = nodes.length > 0 ? nodes[nodes.length - 1] : null;
    let newX = 180;
    let newY = 370;

    // If this is the first node (build from scratch), center it
    if (nodes.length === 0) {
      newX = 420;
      newY = 200;
    } else if (lastNode && lastNode.position) {
      newY = lastNode.position.y + VERTICAL_GAP;
    }

    const newNodeId = `node-${nodes.length + 1}`;

    // Create the new node with the selected activity name
    const newNode = {
      id: newNodeId,
      data: { label: activityName } as NodeData,
      position: { x: newX, y: newY },
      style: { ...nodeDefaultStyle },
      type: 'custom',
    };

    console.log('Creating new node:', newNode);

    // If in subflow mode, consider the new node as part of the active subflow
    if (isSubflowMode && activeNodeId) {
      // Add a subflow identifier to track this node as part of the current subflow
      newNode.data = {
        ...newNode.data,
        subflowParent: activeNodeId
      };
    }

    setNodes((nds) => {
      const updatedNodes = [...nds, newNode];
      console.log('Updated nodes:', updatedNodes);
      return updatedNodes;
    });

    // If we're in inventory mode, update the inventory nodes too
    if (!isScenarioMode && !isSubflowMode) {
      setInventoryNodes(invNodes => [...invNodes, newNode]);
    }

    toast({
      title: "New Node Added",
      description: `Added "${activityName}" to the diagram`
    });
  };

  const handleAddFinalOutputNode = () => {
    const newNodeId = `final-output-${Date.now()}`;
    // Calculate position to place it toward the right side of existing nodes
    let maxX = 420;
    let centerY = 300;
    
    if (nodes.length > 0) {
      // Find the rightmost node position
      nodes.forEach(node => {
        if (node.position.x > maxX) {
          maxX = node.position.x;
        }
      });
      
      // Calculate average Y position for better placement
      const sumY = nodes.reduce((sum, node) => sum + node.position.y, 0);
      centerY = nodes.length > 0 ? sumY / nodes.length : 300;
      
      // Place the final output node to the right of the rightmost node
      maxX += 300; // Add some space to the right
    }
    
    const newNode = {
      id: newNodeId,
      type: 'finalOutput',
      data: { label: 'Final Product' },
      position: { x: maxX, y: centerY },
      style: {},
    };
    
    console.log('Adding final output node:', newNode);
    
    setNodes((nds) => {
      const updatedNodes = [...nds, newNode];
      console.log('Updated nodes:', updatedNodes);
      return updatedNodes;
    });
    
    toast({ title: 'Added Final Output', description: 'Final output node added to the diagram.' });
  };

  function getNodeOutputs(nodeId) {
    const node = nodes.find(n => n.id === nodeId);
    if (!node || !node.data || !node.data.outputs) return null;
    return { node, outputs: node.data.outputs };
  }

  function getNodeById(id) {
    return nodes.find(node => node.id === id);
  }
  
  // Helper function to find incoming connections for a node
  function findIncomingConnections(nodeId) {
    return edges.filter(edge => edge.target === nodeId);
  }

  // Get data from a connection by edge id
  function getConnectionDataByEdgeId(edgeId) {
    const edge = edges.find(e => e.id === edgeId);
    return edge?.data || null;
  }

  // Handle connection start (when user starts dragging from a node)
  const onConnectStart = useCallback((event, params) => {
    console.log("Connection start detected:", params);
    setConnectionStartParams(params);
  }, []);

  // Handle connection end (when user releases drag on another node)
  const onConnectEnd = useCallback((event) => {
    // Get the node under the mouse when the connection drag ends
    const targetElement = document.elementFromPoint(event.clientX, event.clientY);

    if (!targetElement) {
      setConnectionStartParams(null);
      return;
    }

    // Get the node ID from the DOM element - React Flow specific approach
    const nodeElement = targetElement.closest('.react-flow__node');
    let targetNodeId = null;

    if (nodeElement) {
      // React Flow stores node ID in data-id attribute
      targetNodeId = nodeElement.getAttribute('data-id');

      // If not found, try other common attributes
      if (!targetNodeId) {
        targetNodeId = nodeElement.getAttribute('data-nodeid') ||
                      nodeElement.id ||
                      nodeElement.dataset?.id ||
                      nodeElement.dataset?.nodeid;
      }

      // Last resort: check if the element has a class that contains the node ID
      if (!targetNodeId) {
        const classList = Array.from(nodeElement.classList);
        const nodeIdClass = classList.find(cls => cls.startsWith('react-flow__node-'));
        if (nodeIdClass) {
          targetNodeId = nodeIdClass.replace('react-flow__node-', '');
        }
      }
    }

    console.log("Connection end detected on node:", targetNodeId);
    console.log("Target element:", targetElement);
    console.log("Node element:", nodeElement);
    
    if (connectionStartParams && targetNodeId) {
      // Get the source node and target node from the connection
      const sourceNode = getNodeById(connectionStartParams.nodeId);
      const targetNode = getNodeById(targetNodeId);

      console.log("Source node found:", sourceNode);
      console.log("Target node found:", targetNode);
      
      if (sourceNode && targetNode && sourceNode.id !== targetNode.id) {
        console.log("Opening connection form for nodes:", sourceNode.id, "->", targetNode.id);
        
        // Instead of adding an edge, open the connection form with the source and target nodes
        setConnectionSourceNode(sourceNode);
        setTargetNodeId(targetNodeId);
        
        // Get output data for autofill
        const sources = [];
        const outputObj = getNodeOutputs(sourceNode.id);
        if (outputObj && outputObj.outputs) {
          sources.push({
            nodeName: sourceNode.data?.label || sourceNode.id,
            outputs: outputObj.outputs,
          });
        }
        
        // Filter available nodes to just this target node
        setAvailableTargetNodes([targetNode]);
        setAutoFillInputs(sources || []);
        
        // Check if the source node has incoming connections
        const incomingEdges = findIncomingConnections(sourceNode.id);
        
        // If there's an incoming connection, use its output data to pre-fill the form
        if (incomingEdges.length > 0) {
          const firstIncomingEdge = incomingEdges[0];
          const connectionData = getConnectionDataByEdgeId(firstIncomingEdge.id);
          
          if (connectionData) {
            setIncomingConnectionData(connectionData);
          } else {
            // If no data is available in the edge, try to get output data from the source node
            const sourceNodeOfIncomingEdge = getNodeById(firstIncomingEdge.source);
            if (sourceNodeOfIncomingEdge && sourceNodeOfIncomingEdge.data) {
              const sourceOutputs = sourceNodeOfIncomingEdge.data.outputs || {};
              const derivedData = {
                energyOutput: {
                  energy: sourceOutputs.energy || "",
                  unit: sourceOutputs.energyUnit || "GJ",
                  sec: sourceOutputs.energySEC || ""
                },
                matOutput: {
                  material: sourceNodeOfIncomingEdge.data.material || "",
                  unit: sourceNodeOfIncomingEdge.data.materialUnit || "Tonnes",
                  smc: sourceNodeOfIncomingEdge.data.materialSMC || ""
                }
              };
              setIncomingConnectionData(derivedData);
            }
          }
        } else {
          setIncomingConnectionData(null);
        }
        
        setShowConnectionDialog(true);
      }
    }
    
    setConnectionStartParams(null);
  }, [connectionStartParams, nodes]);

  // This function is modified to not automatically create edges but open the connection form
  const onConnect = useCallback((params: Connection) => {
    // Get the source node from the connection params
    const sourceNode = getNodeById(params.source);
    const targetNode = getNodeById(params.target);

    // Determine which node the user started dragging from using connectionStartParams
    let dragStartNode = sourceNode;
    let dragEndNode = targetNode;

    if (connectionStartParams) {
      // The node the user started dragging from should always be the one we open the dialog for
      dragStartNode = getNodeById(connectionStartParams.nodeId);
      // The other node is the drag end node
      dragEndNode = dragStartNode?.id === sourceNode?.id ? targetNode : sourceNode;
    }

    if (dragStartNode && dragEndNode && dragStartNode.id !== dragEndNode.id) {
      // Set up the connection form with the node the user started dragging from
      setConnectionSourceNode(dragStartNode);
      setTargetNodeId(dragEndNode.id);

      // Get output data for autofill
      const sources = [];
      const nodeData = getNodeTechnologyAndOutputs(dragStartNode.id);
      if (nodeData) {
        sources.push(nodeData);
      }

      // Filter available nodes to exclude the drag start node
      const filteredNodes = nodes.filter(node => node.id !== dragStartNode.id);
      setAvailableTargetNodes(filteredNodes);

      setAutoFillInputs(sources || []);

      // Check if the drag start node has incoming connections
      const incomingEdges = findIncomingConnections(dragStartNode.id);

      if (incomingEdges.length > 0) {
        const firstIncomingEdge = incomingEdges[0];
        const connectionData = getConnectionDataByEdgeId(firstIncomingEdge.id);

        if (connectionData && connectionData.type !== 'input-material' && connectionData.type !== 'input-energy') {
          setIncomingConnectionData(connectionData);
        } else {
          setIncomingConnectionData(null);
        }
      } else {
        setIncomingConnectionData(null);
      }

      setShowConnectionDialog(true);
    }
  }, [nodes, edges, connectionStartParams]);

  // New function to handle opening the connection form from the Technology button
  const handleOpenConnectionForm = useCallback((sourceNode) => {
    if (!sourceNode) {
      return;
    }

    // In scenario mode, use enhanced ConnectionFormDialog with dual tabs
    if (isScenarioMode) {
      // Set up for ConnectionFormDialog with dual tab support
      setConnectionSourceNode(sourceNode);
      setShowConnectionDialog(true);

      // Set default tab to current scenario for editing
      setScenarioTabMode('current');
      return;
    }

    setConnectionSourceNode(sourceNode);

    // Get output data and technology for autofill
    const sources = [];
    const nodeData = getNodeTechnologyAndOutputs(sourceNode.id);
    if (nodeData) {
      sources.push(nodeData);
    }
    
    // Filter available nodes to exclude the source node
    const filteredNodes = nodes.filter(node => node.id !== sourceNode.id);
    setAvailableTargetNodes(filteredNodes);
    
    setAutoFillInputs(sources || []);
    
    // Check if the source node has incoming connections
    const incomingEdges = findIncomingConnections(sourceNode.id);
    
    // If there's an incoming connection, we'll use its output data to pre-fill the form
    if (incomingEdges.length > 0) {
      // Use the first incoming connection
      const firstIncomingEdge = incomingEdges[0];
      
      const connectionData = getConnectionDataByEdgeId(firstIncomingEdge.id);
      console.log("Found incoming connection data for node:", sourceNode.id, connectionData);
      
      // Only set incoming connection data for actual connections, not input edges
      if (connectionData && connectionData.type !== 'input-material' && connectionData.type !== 'input-energy') {
        setIncomingConnectionData(connectionData);
      } else {
        // If no data is available in the edge, try to get output data from the source node
        const sourceNodeOfIncomingEdge = getNodeById(firstIncomingEdge.source);
        if (sourceNodeOfIncomingEdge && sourceNodeOfIncomingEdge.data) {
          const sourceOutputs = sourceNodeOfIncomingEdge.data.outputs || {};
          // Create a compatible format with what we expect in the form
          const derivedData = {
            outputTechnology: sourceNodeOfIncomingEdge.data.technology,
            energyOutput: {
              energy: sourceNodeOfIncomingEdge.data.energy || "",
              unit: sourceNodeOfIncomingEdge.data.energyUnit || "GJ",
              sec: sourceNodeOfIncomingEdge.data.energySEC || ""
            },
            matOutput: {
              material: sourceNodeOfIncomingEdge.data.material || "",
              unit: sourceNodeOfIncomingEdge.data.materialUnit || "Tonnes",
              smc: sourceNodeOfIncomingEdge.data.materialSMC || ""
            }
          };
          setIncomingConnectionData(derivedData);
        }
      }
    } else {
      // Clear any previous incoming connection data if there are no incoming edges
      setIncomingConnectionData(null);
    }

    setShowConnectionDialog(true);
  }, [nodes, edges]);

  // New helper function to prepare multiple output connections
  const prepareMultipleOutputConnections = (formData, sourceNode) => {
    // Array to hold all the connection information
    const connections = [];

    // Process energy outputs if they exist
    if (formData.outputs && formData.outputs.length > 0) {
      formData.outputs.forEach(output => {
        // For each output, process all energy outputs
        if (output.energyOutputs && output.energyOutputs.length > 0) {
          output.energyOutputs.forEach(energyOutput => {
            // Only create connections for non-final outputs with a destination
            if (!energyOutput.final && energyOutput.connect && energyOutput.destinationTechnology) {
              // Make sure we have a valid target and energy name
              if (energyOutput.connect && energyOutput.energy) {
                connections.push({
                  sourceId: sourceNode.id,
                  targetId: energyOutput.connect,
                  label: energyOutput.energy,
                  data: {
                    type: 'energy',
                    energy: energyOutput.energy,
                    unit: energyOutput.unit,
                    qty: energyOutput.qty,
                    qtyUnit: energyOutput.qtyUnit
                  }
                });
              }
            }
          });
        }

        // For each output, process all material outputs
        if (output.matOutputs && output.matOutputs.length > 0) {
          output.matOutputs.forEach(matOutput => {
            // Only create connections for non-final outputs with a destination
            if (!matOutput.final && matOutput.connect && matOutput.destinationTechnology) {
              // Make sure we have a valid target and material name
              if (matOutput.connect && matOutput.material) {
                connections.push({
                  sourceId: sourceNode.id,
                  targetId: matOutput.connect,
                  label: matOutput.material,
                  data: {
                    type: 'material',
                    material: matOutput.material,
                    unit: matOutput.unit,
                    qty: matOutput.qty,
                    qtyUnit: matOutput.qtyUnit
                  }
                });
              }
            }
          });
        }
      });
    }

    // For backward compatibility, also check the single energy and material output
    // This part can eventually be removed once all forms use the new array format
    if (formData.energyOutput && !formData.energyOutput.final && formData.energyOutput.connect) {
      connections.push({
        sourceId: sourceNode.id,
        targetId: formData.energyOutput.connect,
        label: formData.energyOutput.energy || 'Energy',
        data: {
          type: 'energy',
          ...formData.energyOutput
        }
      });
    }

    if (formData.matOutput && !formData.matOutput.final && formData.matOutput.connect) {
      connections.push({
        sourceId: sourceNode.id,
        targetId: formData.matOutput.connect,
        label: formData.matOutput.material || 'Material',
        data: {
          type: 'material',
          ...formData.matOutput
        }
      });
    }

    console.log("Prepared connections:", connections);
    return connections;
  };

  function handleConnectionComplete(formData: any) {
    // Close dialog first
    setShowConnectionDialog(false);

    // Check if we have a valid source node
    if (!connectionSourceNode) {
      console.error("No source node found");
      toast({
        title: "Connection error",
        description: "Could not create the connection. Please try again." ,
        variant: "destructive"
      });
      return;
    }

    const sourceNodeId = connectionSourceNode.id;
    console.log(`=== SAVING NODE ${sourceNodeId} ===`);

    // Store the completed form data for persistence
    const normalizedFormData = {
      formData: formData.formData || formData,
      outputs: formData.outputs || [],
      technologies: formData.technologies || [],
      technologyFormData: formData.technologyFormData || {},
      completedAt: new Date().toISOString()
    };

    setNodeFormData(prev => ({
      ...prev,
      [sourceNodeId]: normalizedFormData
    }));

    try {
      // STEP 1: Clean up ALL existing edges from/to this node
      console.log(`STEP 1: Cleaning up ALL edges from/to node ${sourceNodeId}`);
      setEdges(eds => {
        const before = eds.length;
        const filtered = eds.filter(edge =>
          edge.source !== sourceNodeId && // Remove outgoing edges
          !(edge.target === sourceNodeId && edge.data?.type?.startsWith('input-')) // Remove incoming input edges
        );
        console.log(`Removed ${before - filtered.length} edges from/to node ${sourceNodeId}`);
        return filtered;
      });

      // STEP 2: Create edges based on CURRENT form state
      console.log(`STEP 2: Creating edges based on current form state`);

      // Create input edges (standalone inputs with sourceActivity = "Nil")
      createInputEdges(formData, sourceNodeId);

      // Create output edges (connections to other nodes)
      createOutputEdges(formData, sourceNodeId);

      // Create byproduct edges
      createByproductEdges(formData, sourceNodeId);

      // Create final output components (for outputs with final: true)
      console.log("=== CALLING createFinalOutputComponents ===");
      console.log("formData structure:", JSON.stringify(formData, null, 2));

      // Create the correct data structure for final output creation
      const finalOutputData = {
        outputs: formData.outputs || []
      };
      console.log("Final output data:", JSON.stringify(finalOutputData, null, 2));
      console.log("Source node ID:", sourceNodeId);
      console.log("Current nodes:", nodes.map(n => ({ id: n.id, label: n.data.label })));

      createFinalOutputComponents(finalOutputData, sourceNodeId);

      // STEP 3: Update node data
      updateNodeData(formData, sourceNodeId);

      // STEP 4: Auto-fit view to show all nodes properly
      setTimeout(() => {
        if (reactFlowRef.current?.fitView) {
          reactFlowRef.current.fitView({ padding: 0.2, duration: 600 });
        }
      }, 200);

      // STEP 5: Clean up
      setConnectionSourceNode(null);
      setAutoFillInputs([]);
      setIncomingConnectionData(null);
      setTargetNodeId(null);

      toast({
        title: 'Node Saved',
        description: `Updated ${connectionSourceNode.data.label} successfully`
      });

    } catch (error) {
      console.error("Error saving node:", error);
      toast({
        title: "Save error",
        description: "An error occurred while saving. Please try again.",
        variant: "destructive"
      });
    }
  }

  // Simple, deterministic edge creation functions
  function createInputEdges(formData: any, sourceNodeId: string, currentNodes: any[] = nodes) {
    console.log("Creating input edges for node:", sourceNodeId);
    console.log("Form data materialInputs:", formData.materialInputs);
    console.log("Form data energyInputs:", formData.energyInputs);

    // For Gas Oil Separation (node 1), we need to check both the main form data and technology-specific data
    let materialInputs = formData.materialInputs || [];
    let energyInputs = formData.energyInputs || [];

    // If we have technology form data, use that instead
    if (formData.technologyFormData && formData.technologies && formData.technologies.length > 0) {
      const activeTech = formData.technologies[0]; // Use first technology
      const techData = formData.technologyFormData[activeTech];
      if (techData) {
        console.log("Using technology-specific data for inputs:", techData);
        materialInputs = techData.materialInputs || materialInputs;
        energyInputs = techData.energyInputs || energyInputs;
      }
    }

    console.log("Final materialInputs to process:", materialInputs);
    console.log("Final energyInputs to process:", energyInputs);

    // Handle material inputs with sourceActivity = "Nil"
    if (materialInputs && Array.isArray(materialInputs)) {
      materialInputs.forEach((input: any, index: number) => {
        if (input.sourceActivity === "Nil" && input.material && input.material.trim() !== "") {
          console.log(`Creating material input edge: ${input.material}`);

          const virtualInputId = `virtual-input-${sourceNodeId}-material-${index}`;
          const edgeId = `input-${sourceNodeId}-material-${index}`;

          // Create virtual input node with proper positioning
          const targetNode = currentNodes.find(n => n.id === sourceNodeId);
          if (targetNode) {
            const virtualInputNode = {
              id: virtualInputId,
              data: { label: input.material },
              position: {
                x: targetNode.position.x - 250, // Further left for better spacing
                y: targetNode.position.y - 20 + (index * 80) // Better vertical spacing
              },
              style: { width: 1, height: 1, opacity: 0, pointerEvents: 'none' },
              type: 'input',
            };

            setNodes(nds => [...nds.filter(n => n.id !== virtualInputId), virtualInputNode]);

            // Create input edge with proper connection handles
            const inputEdge = {
              id: edgeId,
              source: virtualInputId,
              target: sourceNodeId,
              targetHandle: 'left-target', // Connect to left side of target node
              label: input.material,
              data: {
                type: 'input-material',
                material: input.material,
                sourceActivity: 'Nil',
                technology: 'Nil'
              },
              style: { stroke: "#10B981", strokeWidth: 3, opacity: 0.9 },
              labelStyle: {
                fill: '#787882',
                fontWeight: 400,
                fontSize: 13.5,
                userSelect: "none",
                background: "rgba(255,255,255,0.7)",
                padding: "2px 5px",
                borderRadius: "3px",
                border: "1px solid #ded3fd",
              },
              markerEnd: { type: MarkerType.ArrowClosed, color: "#10B981" },
              type: 'smoothstep'
            };

            setEdges(eds => [...eds.filter(e => e.id !== edgeId), inputEdge]);
          }
        }
      });
    }

    // Handle energy inputs with sourceActivity = "Nil"
    if (energyInputs && Array.isArray(energyInputs)) {
      energyInputs.forEach((input: any, index: number) => {
        if (input.sourceActivity === "Nil" && input.source && input.source.trim() !== "") {
          console.log(`Creating energy input edge: ${input.source}`);

          const virtualInputId = `virtual-input-${sourceNodeId}-energy-${index}`;
          const edgeId = `input-${sourceNodeId}-energy-${index}`;

          // Create virtual input node
          const targetNode = nodes.find(n => n.id === sourceNodeId);
          if (targetNode) {
            const virtualInputNode = {
              id: virtualInputId,
              data: { label: input.source },
              position: {
                x: targetNode.position.x - 250, // Further left for better spacing
                y: targetNode.position.y + 60 + (index * 80) // Better offset from material inputs
              },
              style: { width: 1, height: 1, opacity: 0, pointerEvents: 'none' },
              type: 'input',
            };

            setNodes(nds => [...nds.filter(n => n.id !== virtualInputId), virtualInputNode]);

            // Create input edge with proper connection handles
            const inputEdge = {
              id: edgeId,
              source: virtualInputId,
              target: sourceNodeId,
              targetHandle: 'left-target', // Connect to left side of target node
              label: input.source,
              data: {
                type: 'input-energy',
                energy: input.source,
                sourceActivity: 'Nil',
                technology: 'Nil'
              },
              style: { stroke: "#10B981", strokeWidth: 3, opacity: 0.9 },
              labelStyle: {
                fill: '#787882',
                fontWeight: 400,
                fontSize: 13.5,
                userSelect: "none",
                background: "rgba(255,255,255,0.7)",
                padding: "2px 5px",
                borderRadius: "3px",
                border: "1px solid #ded3fd",
              },
              markerEnd: { type: MarkerType.ArrowClosed, color: "#10B981" },
              type: 'smoothstep'
            };

            setEdges(eds => [...eds.filter(e => e.id !== edgeId), inputEdge]);
          }
        }
      });
    }
  }

  function createOutputEdges(formData: any, sourceNodeId: string) {
    console.log("Creating output edges...");

    if (formData.outputs && Array.isArray(formData.outputs)) {
      formData.outputs.forEach((output: any, outputIndex: number) => {

        // Handle material outputs
        if (output.matOutputs && Array.isArray(output.matOutputs)) {
          output.matOutputs.forEach((matOutput: any, matIndex: number) => {
            if (matOutput.material && matOutput.material.trim() !== "" &&
                matOutput.connect && !matOutput.final) {

              console.log(`Creating material output edge: ${matOutput.material} -> ${matOutput.connect}`);

              const edgeId = `output-${sourceNodeId}-${matOutput.connect}-material-${outputIndex}-${matIndex}`;

              // Determine connection handles based on node positions
              const sourceNode = nodes.find(n => n.id === sourceNodeId);
              const targetNode = nodes.find(n => n.id === matOutput.connect);
              let sourceHandle = 'right-source';
              let targetHandle = 'left-target';

              if (sourceNode && targetNode) {
                // If target is below source, use vertical connection
                if (targetNode.position.y > sourceNode.position.y + 100) {
                  sourceHandle = 'bottom-source';
                  targetHandle = 'top-target';
                }
                // If target is to the left of source, use reverse horizontal
                else if (targetNode.position.x < sourceNode.position.x) {
                  sourceHandle = 'left-source';
                  targetHandle = 'right-target';
                }
              }

              const outputEdge = {
                id: edgeId,
                source: sourceNodeId,
                sourceHandle: sourceHandle,
                target: matOutput.connect,
                targetHandle: targetHandle,
                label: matOutput.material, // Always use material name as label
                data: {
                  type: 'output-material',
                  material: matOutput.material,
                  technology: output.outputTechnology || 'Boiler'
                },
                style: { stroke: '#9b87f5', strokeWidth: 2, opacity: 0.85 },
                labelStyle: {
                  fill: '#787882',
                  fontWeight: 400,
                  fontSize: 13.5,
                  userSelect: "none",
                  background: "rgba(255,255,255,0.7)",
                  padding: "2px 5px",
                  borderRadius: "3px",
                  border: "1px solid #ded3fd",
                },
                markerEnd: { type: MarkerType.ArrowClosed, color: "#9b87f5" },
                type: 'smoothstep'
              };

              setEdges(eds => [...eds.filter(e => e.id !== edgeId), outputEdge]);
            }
          });
        }

        // Handle energy outputs
        if (output.energyOutputs && Array.isArray(output.energyOutputs)) {
          output.energyOutputs.forEach((energyOutput: any, energyIndex: number) => {
            if (energyOutput.energy && energyOutput.energy.trim() !== "" &&
                energyOutput.connect && !energyOutput.final) {

              console.log(`Creating energy output edge: ${energyOutput.energy} -> ${energyOutput.connect}`);

              const edgeId = `output-${sourceNodeId}-${energyOutput.connect}-energy-${outputIndex}-${energyIndex}`;

              // Determine connection handles based on node positions
              const sourceNode = nodes.find(n => n.id === sourceNodeId);
              const targetNode = nodes.find(n => n.id === energyOutput.connect);
              let sourceHandle = 'right-source';
              let targetHandle = 'left-target';

              if (sourceNode && targetNode) {
                // If target is below source, use vertical connection
                if (targetNode.position.y > sourceNode.position.y + 100) {
                  sourceHandle = 'bottom-source';
                  targetHandle = 'top-target';
                }
                // If target is to the left of source, use reverse horizontal
                else if (targetNode.position.x < sourceNode.position.x) {
                  sourceHandle = 'left-source';
                  targetHandle = 'right-target';
                }
              }

              const outputEdge = {
                id: edgeId,
                source: sourceNodeId,
                sourceHandle: sourceHandle,
                target: energyOutput.connect,
                targetHandle: targetHandle,
                label: energyOutput.energy, // Always use energy name as label
                data: {
                  type: 'output-energy',
                  energy: energyOutput.energy,
                  technology: output.outputTechnology || 'Boiler'
                },
                style: { stroke: '#9b87f5', strokeWidth: 2, opacity: 0.85 },
                labelStyle: {
                  fill: '#787882',
                  fontWeight: 400,
                  fontSize: 13.5,
                  userSelect: "none",
                  background: "rgba(255,255,255,0.7)",
                  padding: "2px 5px",
                  borderRadius: "3px",
                  border: "1px solid #ded3fd",
                },
                markerEnd: { type: MarkerType.ArrowClosed, color: "#9b87f5" },
                type: 'smoothstep'
              };

              setEdges(eds => [...eds.filter(e => e.id !== edgeId), outputEdge]);
            }
          });
        }
      });
    }
  }

  function createByproductEdges(formData: any, sourceNodeId: string) {
    console.log("Creating byproduct edges...");

    // Handle material byproducts
    const materialByProducts = formData.materialByProducts
      ? (typeof formData.materialByProducts === 'string'
          ? JSON.parse(formData.materialByProducts)
          : formData.materialByProducts)
      : [];

    if (materialByProducts && Array.isArray(materialByProducts)) {
      materialByProducts.forEach((byproduct: any, index: number) => {
        if (byproduct.byproduct && byproduct.byproduct.trim() !== "") {
          console.log(`Creating material byproduct edge: ${byproduct.byproduct}`);

          const edgeId = `byproduct-${sourceNodeId}-material-${index}`;

          // Determine connection handles based on node positions (if connecting to another node)
          let sourceHandle = 'right-source';
          let targetHandle = 'left-target';

          if (byproduct.connect) {
            const sourceNode = nodes.find(n => n.id === sourceNodeId);
            const targetNode = nodes.find(n => n.id === byproduct.connect);

            if (sourceNode && targetNode) {
              // If target is below source, use vertical connection
              if (targetNode.position.y > sourceNode.position.y + 100) {
                sourceHandle = 'bottom-source';
                targetHandle = 'top-target';
              }
              // If target is to the left of source, use reverse horizontal
              else if (targetNode.position.x < sourceNode.position.x) {
                sourceHandle = 'left-source';
                targetHandle = 'right-target';
              }
            }
          }

          const byproductEdge = {
            id: edgeId,
            source: sourceNodeId,
            sourceHandle: byproduct.connect ? sourceHandle : undefined,
            target: byproduct.connect || null, // null for standalone byproducts
            targetHandle: byproduct.connect ? targetHandle : undefined,
            label: byproduct.byproduct,
            data: {
              type: 'byproduct-material',
              material: byproduct.byproduct,
              technology: formData.byproductTechnology || 'Boiler'
            },
            style: { stroke: '#c084fc', strokeWidth: 2, opacity: 0.85 },
            markerEnd: byproduct.connect ? { type: MarkerType.ArrowClosed, color: "#c084fc" } : undefined,
            animated: true,
            type: 'smoothstep'
          };

          setEdges(eds => [...eds.filter(e => e.id !== edgeId), byproductEdge]);
        }
      });
    }

    // Handle energy byproducts
    const energyByProducts = formData.energyByProducts
      ? (typeof formData.energyByProducts === 'string'
          ? JSON.parse(formData.energyByProducts)
          : formData.energyByProducts)
      : [];

    if (energyByProducts && Array.isArray(energyByProducts)) {
      energyByProducts.forEach((byproduct: any, index: number) => {
        if (byproduct.byproduct && byproduct.byproduct.trim() !== "") {
          console.log(`Creating energy byproduct edge: ${byproduct.byproduct}`);

          const edgeId = `byproduct-${sourceNodeId}-energy-${index}`;

          // Determine connection handles based on node positions (if connecting to another node)
          let sourceHandle = 'right-source';
          let targetHandle = 'left-target';

          if (byproduct.connect) {
            const sourceNode = nodes.find(n => n.id === sourceNodeId);
            const targetNode = nodes.find(n => n.id === byproduct.connect);

            if (sourceNode && targetNode) {
              // If target is below source, use vertical connection
              if (targetNode.position.y > sourceNode.position.y + 100) {
                sourceHandle = 'bottom-source';
                targetHandle = 'top-target';
              }
              // If target is to the left of source, use reverse horizontal
              else if (targetNode.position.x < sourceNode.position.x) {
                sourceHandle = 'left-source';
                targetHandle = 'right-target';
              }
            }
          }

          const byproductEdge = {
            id: edgeId,
            source: sourceNodeId,
            sourceHandle: byproduct.connect ? sourceHandle : undefined,
            target: byproduct.connect || null, // null for standalone byproducts
            targetHandle: byproduct.connect ? targetHandle : undefined,
            label: byproduct.byproduct,
            data: {
              type: 'byproduct-energy',
              energy: byproduct.byproduct,
              technology: formData.byproductTechnology || 'Boiler'
            },
            style: { stroke: '#4ade80', strokeWidth: 2, opacity: 0.85 },
            markerEnd: byproduct.connect ? { type: MarkerType.ArrowClosed, color: "#4ade80" } : undefined,
            animated: true,
            type: 'smoothstep'
          };

          setEdges(eds => [...eds.filter(e => e.id !== edgeId), byproductEdge]);
        }
      });
    }
  }

  function updateNodeData(formData: any, sourceNodeId: string) {
    console.log("Updating node data...");

    setNodes(nds =>
      nds.map(node => {
        if (node.id === sourceNodeId) {
          return {
            ...node,
            data: {
              ...node.data,
              technology: formData.outputTechnology || formData.technology || formData.byproductTechnology || 'Boiler'
            }
          }
        }
        return node;
      })
    );
  }


  
  // Helper function to generate tooltip content from form data
  function generateTooltipContent(formData: any) {
    const inputs = [];
    const outputs = [];
    
    // Add energy inputs if available
    if (formData.energyInput?.source) {
      inputs.push(`Energy: ${formData.energyInput.source} (${formData.energyInput.unit})`);
    }
    
    // Add material inputs if available
    if (formData.matInput?.material) {
      inputs.push(`Material: ${formData.matInput.material} (${formData.matInput.unit})`);
    }
    
    // Add energy outputs if available
    if (formData.energyOutput?.energy) {
      outputs.push(`Energy: ${formData.energyOutput.energy} (${formData.energyOutput.unit})`);
    }
    
    // Add material outputs if available
    if (formData.matOutput?.material) {
      outputs.push(`Material: ${formData.matOutput.material} (${formData.matOutput.unit})`);
    }
    
    return `Inputs: ${inputs.join(', ') || 'None'}\nOutputs: ${outputs.join(', ') || 'None'}`;
  }

  function handleConnectionModalClosed() {
    // Clean up when connection modal is closed without completion
    setShowConnectionDialog(false);
    setConnectionSourceNode(null);
    setAutoFillInputs([]);
    setAvailableTargetNodes([]);
    setIncomingConnectionData(null);
    setTargetNodeId(null);
  }
  
  const handleNodeDoubleClick = (event, node) => {
    event.preventDefault();

    // Disable double-click in scenario mode
    if (isScenarioMode) {
      console.log('Node double-click disabled in scenario mode');
      return;
    }

    console.log('Node double-clicked:', node.id, node.data.label);
    setEditNode({ id: node.id, label: node.data.label });
  };

  const handleUpdateNodeName = (nodeId: string, newLabel: string) => {
    console.log('Updating node name:', nodeId, 'New label:', newLabel);
    
    setNodes((nds) =>
      nds.map((node) => {
        if (node.id === nodeId) {
          console.log('Found node to update:', node.id, 'New label:', newLabel);
          return { 
            ...node, 
            data: { 
              ...node.data, 
              label: newLabel 
            } 
          };
        }
        return node;
      })
    );
    
    // Also update inventory nodes if not in scenario mode
    if (!isScenarioMode && !isSubflowMode) {
      setInventoryNodes((invNodes) =>
        invNodes.map((node) => {
          if (node.id === nodeId) {
            return { 
              ...node, 
              data: { 
                ...node.data, 
                label: newLabel 
              } 
            };
          }
          return node;
        })
      );
    }
    
    console.log('Node name updated successfully');
  };

  const handleEdgeDoubleClick = (event, edge) => {
    event.preventDefault();
    setEditEdge({ id: edge.id, label: edge.label || '' });
  };

  const handleUpdateEdgeName = () => {
    if (editEdge) {
      setEdges((eds) =>
        eds.map((edge) =>
          edge.id === editEdge.id ? { ...edge, label: editEdge.label } : edge
        )
      );
      setEditEdge(null);
    }
  };

  const handleModeSelect = async (mode: "base" | "scratch") => {
    setFlowMode(mode);
    setShowModeDialog(false);

    // If base mode is selected, load the sector-specific default flow or fallback to standard template
    if (mode === "base") {
      console.log("Loading sector-specific default flow...");

      // Get sector UUID from industry ID
      const sectorUuid = await getSectorUuidFromIndustryId(industryId || '');
      
      // Find the sector and its default flow
      const sector = sectors.find(s => s.uuid === sectorUuid);
      const defaultFlow = sector?.default_flow;

      console.log('=== SECTOR DEFAULT FLOW DEBUG ===');
      console.log('Industry ID:', industryId);
      console.log('Sector UUID:', sectorUuid);
      console.log('Found sector:', sector?.name);
      console.log('Has default flow:', !!defaultFlow);
      console.log('Default flow data:', defaultFlow);
      console.log('==================================');

      let formDataMap: Record<string, any>;
      let initialNodesData: any[];

      if (defaultFlow) {
        console.log("Using sector-specific default flow for sector:", sector?.name);
        
        // Convert the default flow to the format used by the inventory system
        formDataMap = convertDefaultFlowToFormData(defaultFlow);
        initialNodesData = convertDefaultFlowToInitialNodes(defaultFlow);

        // Set the converted form data
        setNodeFormData(formDataMap);
      } else {
        console.log("No sector-specific default flow found, starting with blank flow");
        
        // Start with blank flow - no fallback to hardcoded template
        formDataMap = {};
        initialNodesData = [];
        
        // Set empty form data
        setNodeFormData({});
      }

      // STEP 2: Create a simplified schema from the template
      const templateSchema: IndustryFlowSchema = {
        name: defaultFlow ? (sector?.name ? `${sector.name} Template` : 'Standard Template') : 'Blank Flow',
        flowType: 'inventory',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        nodeData: {},
        industryId: industryId
      };

      // Convert nodes and form data to the clean schema format
      initialNodesData.forEach(node => {
        const formData = formDataMap[node.id];
        if (formData) {
          templateSchema.nodeData[node.id] = {
            id: node.id,
            label: node.data.label,
            type: node.type,
            position: node.position,
            formData: {
              activity: formData.formData?.activity || node.data.label,
              currentTechnology: formData.formData?.technology || 'Boiler',
              customTechnology: formData.formData?.customTechnology,
              customActivity: formData.formData?.customActivity,

              // Extract material inputs from technology form data (where they're actually stored)
              materialInputs: (() => {
                const techFormData = formData.technologyFormData?.[formData.formData?.technology || 'Boiler'];
                return techFormData?.materialInputs || formData.formData?.materialInputs || [];
              })(),
              // Extract energy inputs from technology form data
              energyInputs: (() => {
                const techFormData = formData.technologyFormData?.[formData.formData?.technology || 'Boiler'];
                return techFormData?.energyInputs || formData.formData?.energyInputs || [];
              })(),
              // Extract emissions from technology form data
              emissions: (() => {
                const techFormData = formData.technologyFormData?.[formData.formData?.technology || 'Boiler'];
                return techFormData?.emissions || formData.formData?.emissions || [];
              })(),

              // Convert outputs from separate field to form data
              outputs: (formData.outputs || []).map(output => ({
                id: output.id,
                targetNode: output.targetNode,
                outputTechnology: output.outputTechnology,
                energyOutputs: (output.energyOutputs || []).map(energyOut => ({
                  id: energyOut.id,
                  energy: energyOut.energy,
                  unit: energyOut.unit,
                  specificEnergyCost: energyOut.sec || '',
                  isFinalOutput: energyOut.final || false,
                  connectToNode: energyOut.connect || '',
                  quantity: energyOut.qty || '',
                  quantityUnit: energyOut.qtyUnit || '',
                  destinationTechnology: energyOut.destinationTechnology
                })),
                materialOutputs: (output.matOutputs || []).map(materialOut => ({
                  id: materialOut.id,
                  material: materialOut.material,
                  unit: materialOut.unit,
                  specificMaterialCost: materialOut.smc || '',
                  isFinalOutput: materialOut.final || false,
                  connectToNode: materialOut.connect || '',
                  quantity: materialOut.qty || '',
                  quantityUnit: materialOut.qtyUnit || '',
                  destinationTechnology: materialOut.destinationTechnology
                }))
              })),

              // Extract byproducts from technology form data
              materialByProducts: (() => {
                const techFormData = formData.technologyFormData?.[formData.formData?.technology || 'Boiler'];
                return techFormData?.materialByProducts || formData.formData?.materialByProducts || [];
              })(),
              energyByProducts: (() => {
                const techFormData = formData.technologyFormData?.[formData.formData?.technology || 'Boiler'];
                return techFormData?.energyByProducts || formData.formData?.energyByProducts || [];
              })(),

              // Extract financial data from technology form data
              financial: (() => {
                const techFormData = formData.technologyFormData?.[formData.formData?.technology || 'Boiler'];
                return techFormData?.financial || formData.formData?.financial || {
                  capacity: '1',
                  capacityUnit: 'Units/day',
                  capitalCost: '1',
                  operatingMaintenanceCost: '1'
                };
              })(),
              financialEntries: (() => {
                const techFormData = formData.technologyFormData?.[formData.formData?.technology || 'Boiler'];
                return techFormData?.financialEntries || formData.formData?.financialEntries || {};
              })()
            },
            technologies: (formData.technologies || []).map(techName => ({
              name: techName,
              formData: formData.technologyFormData?.[techName] || {
                activity: node.data.label,
                technology: techName,
                parameters: {}
              }
            })),
            completedAt: formData.completedAt || new Date().toISOString()
          };
        }
      });

      // Virtual input nodes and final output nodes will be generated from form data
      // No need to add them to the schema

      console.log('Template schema created:', templateSchema);

      // STEP 3: Generate nodes and edges from the schema (same as restore)
      const { nodes: generatedNodes, edges: generatedEdges } = generateFlowFromAnySchema(templateSchema);

      // STEP 4: Set the generated nodes and edges
      setNodes(generatedNodes);
      setEdges(generatedEdges);

      // STEP 5: Generate final output nodes and edges from form data
      setTimeout(() => {
        generateFinalOutputsFromFormData(generatedNodes, formDataMap);
      }, 100); // Short delay to ensure nodes are loaded

      console.log(defaultFlow ? 'Sector-specific template loaded' : 'Standard template loaded');
      console.log('Generated nodes:', generatedNodes.length);
      console.log('Generated edges:', generatedEdges.length);

      // Set a default scenario name if none exists
      if (!scenarioName || scenarioName.trim() === '') {
        setScenarioName(defaultFlow ? (sector?.name ? `${sector.name} Template` : 'Standard Template') : 'Blank Flow');
      }

      toast({
        title: defaultFlow ? "Sector Template Loaded" : "Blank Flow Started",
        description: defaultFlow ? `${sector?.name} template loaded with predefined nodes and connections.` : "Started with a blank flow. Add your first activity to begin."
      });
    }
  };

  // Function to generate only final output nodes and edges from form data
  function generateFinalOutputsFromFormData(nodesList = nodes, formDataMap?: Record<string, any>) {
    console.log("Generating final outputs from form data...");
    console.log("Available nodes:", nodesList.map(n => ({ id: n.id, label: n.data.label })));

    // Use the provided formDataMap or fall back to defaultNodeFormDataMap
    const dataMap = formDataMap || defaultNodeFormDataMap;

    // Process each node's form data to create final output nodes and edges
    Object.entries(dataMap).forEach(([nodeId, nodeFormData]) => {
      console.log(`Checking node ${nodeId} for final outputs...`);

      // Use the comprehensive final output creation function
      createFinalOutputComponentsForTemplate(nodeFormData, nodeId, nodesList);
    });
  }

  // Function to create a final output node and connecting edge
  function createFinalOutputNodeAndEdge(sourceNodeId: string, materialName: string, nodesList = nodes) {
    console.log(`createFinalOutputNodeAndEdge called: sourceNodeId=${sourceNodeId}, materialName=${materialName}`);

    const sourceNode = nodesList.find(n => n.id === sourceNodeId);
    if (!sourceNode) {
      console.log(`Source node ${sourceNodeId} not found in nodesList:`, nodesList.map(n => n.id));
      return;
    }

    // Create final output node ID
    const finalOutputId = `final-output-${sourceNodeId}`;

    // Check if final output node already exists
    const existingFinalOutput = nodes.find(n => n.id === finalOutputId);
    if (existingFinalOutput) return; // Don't create duplicates

    // Position the final output node below the source node
    const finalOutputNode = {
      id: finalOutputId,
      type: 'finalOutput',
      data: {
        label: materialName,
        outputs: { material: materialName }
      },
      position: {
        x: sourceNode.position.x,
        y: sourceNode.position.y + 200
      },
      style: {},
    };

    // Create connecting edge
    const finalOutputEdge = {
      id: `edge-${sourceNodeId}-${finalOutputId}`,
      source: sourceNodeId,
      sourceHandle: 'bottom-source',
      target: finalOutputId,
      targetHandle: 'top-target',
      label: materialName,
      style: { ...edgeStyle, stroke: "#F59E0B" }, // Golden color for final output edges
      labelStyle: edgeLabelStyle,
      type: 'smoothstep',
      markerEnd: { type: MarkerType.ArrowClosed, color: "#F59E0B" },
      data: {
        type: 'final-output',
        material: materialName
      }
    };

    // Add the final output node and edge
    setNodes(prevNodes => [...prevNodes, finalOutputNode]);
    setEdges(prevEdges => [...prevEdges, finalOutputEdge]);
  }

  // Function to generate all edges from the stored form data
  function generateEdgesFromFormData(nodesList = nodes) {
    console.log("Generating edges from form data...");
    console.log("Available nodes:", nodesList.map(n => ({ id: n.id, label: n.data.label })));

    // Process each node's form data to create edges
    Object.entries(defaultNodeFormDataMap).forEach(([nodeId, nodeFormData]) => {
      console.log(`Processing node ${nodeId} form data:`, nodeFormData);

      // Create edges for this node based on its form data
      if (nodeFormData.technologyFormData && nodeFormData.technologies) {
        const activeTech = nodeFormData.technologies[0]; // Use first technology
        const techData = nodeFormData.technologyFormData[activeTech];

        if (techData) {
          console.log(`Creating edges for node ${nodeId} with technology ${activeTech}`);

          // Create input edges
          createInputEdgesForTemplate(techData, nodeId, nodesList);

          // Create output edges
          createOutputEdges(nodeFormData, nodeId);

          // Create byproduct edges
          createByproductEdges(techData, nodeId);

          // Create final output components (for outputs with final: true)
          createFinalOutputComponentsForTemplate(nodeFormData, nodeId, nodesList);
        }
      }
    });

    console.log("Finished generating edges from form data");

    // Auto-fit view to show all nodes and edges properly
    setTimeout(() => {
      if (reactFlowRef.current?.fitView) {
        reactFlowRef.current.fitView({ padding: 0.2, duration: 800 });
      }
    }, 500);
  }

  // Template-specific functions that work with passed nodes list
  function createInputEdgesForTemplate(formData: any, sourceNodeId: string, nodesList: any[]) {
    console.log("Creating input edges for template node:", sourceNodeId);
    console.log("Form data materialInputs:", formData.materialInputs);
    console.log("Form data energyInputs:", formData.energyInputs);

    // Handle material inputs with sourceActivity = "Nil"
    if (formData.materialInputs && Array.isArray(formData.materialInputs)) {
      formData.materialInputs.forEach((input: any, index: number) => {
        if (input.sourceActivity === "Nil" && input.material && input.material.trim() !== "") {
          console.log(`Creating material input edge for template: ${input.material}`);

          const virtualInputId = `virtual-input-${sourceNodeId}-material-${index}`;
          const edgeId = `input-${sourceNodeId}-material-${index}`;

          // Create virtual input node
          const targetNode = nodesList.find(n => n.id === sourceNodeId);
          if (targetNode) {
            const virtualInputNode = {
              id: virtualInputId,
              data: { label: input.material },
              position: {
                x: targetNode.position.x - 250, // Further left for better spacing
                y: targetNode.position.y - 20 + (index * 80) // Better vertical spacing
              },
              style: { width: 1, height: 1, opacity: 0, pointerEvents: 'none' },
              type: 'input',
            };

            setNodes(nds => [...nds.filter(n => n.id !== virtualInputId), virtualInputNode]);

            // Create input edge with proper connection handles
            const inputEdge = {
              id: edgeId,
              source: virtualInputId,
              target: sourceNodeId,
              targetHandle: 'left-target', // Connect to left side of target node
              label: input.material,
              data: {
                type: 'input-material',
                material: input.material,
                sourceActivity: 'Nil',
                technology: 'Nil'
              },
              style: { stroke: "#10B981", strokeWidth: 3, opacity: 0.9 },
              labelStyle: {
                fill: '#787882',
                fontWeight: 400,
                fontSize: 13.5,
                userSelect: "none",
                background: "rgba(255,255,255,0.7)",
                padding: "2px 5px",
                borderRadius: "3px",
                border: "1px solid #ded3fd",
              },
              markerEnd: { type: MarkerType.ArrowClosed, color: "#10B981" },
              type: 'smoothstep'
            };

            setEdges(eds => [...eds.filter(e => e.id !== edgeId), inputEdge]);
          }
        }
      });
    }

    // Handle energy inputs with sourceActivity = "Nil"
    if (formData.energyInputs && Array.isArray(formData.energyInputs)) {
      formData.energyInputs.forEach((input: any, index: number) => {
        if (input.sourceActivity === "Nil" && input.source && input.source.trim() !== "") {
          console.log(`Creating energy input edge for template: ${input.source}`);

          const virtualInputId = `virtual-input-${sourceNodeId}-energy-${index}`;
          const edgeId = `input-${sourceNodeId}-energy-${index}`;

          // Create virtual input node
          const targetNode = nodesList.find(n => n.id === sourceNodeId);
          if (targetNode) {
            const virtualInputNode = {
              id: virtualInputId,
              data: { label: input.source },
              position: {
                x: targetNode.position.x - 250, // Further left for better spacing
                y: targetNode.position.y + 60 + (index * 80) // Better offset from material inputs
              },
              style: { width: 1, height: 1, opacity: 0, pointerEvents: 'none' },
              type: 'input',
            };

            setNodes(nds => [...nds.filter(n => n.id !== virtualInputId), virtualInputNode]);

            // Create input edge with proper connection handles
            const inputEdge = {
              id: edgeId,
              source: virtualInputId,
              target: sourceNodeId,
              targetHandle: 'left-target', // Connect to left side of target node
              label: input.source,
              data: {
                type: 'input-energy',
                energy: input.source,
                sourceActivity: 'Nil',
                technology: 'Nil'
              },
              style: { stroke: "#10B981", strokeWidth: 3, opacity: 0.9 },
              labelStyle: {
                fill: '#787882',
                fontWeight: 400,
                fontSize: 13.5,
                userSelect: "none",
                background: "rgba(255,255,255,0.7)",
                padding: "2px 5px",
                borderRadius: "3px",
                border: "1px solid #ded3fd",
              },
              markerEnd: { type: MarkerType.ArrowClosed, color: "#10B981" },
              type: 'smoothstep'
            };

            setEdges(eds => [...eds.filter(e => e.id !== edgeId), inputEdge]);
          }
        }
      });
    }
  }

  function createFinalOutputComponentsForTemplate(nodeFormData: any, sourceNodeId: string, nodesList: any[]) {
    console.log("Creating final output components for template node:", sourceNodeId);

    if (nodeFormData.outputs && Array.isArray(nodeFormData.outputs)) {
      nodeFormData.outputs.forEach((output: any, outputIndex: number) => {

        // Handle material outputs marked as final
        if (output.matOutputs && Array.isArray(output.matOutputs)) {
          output.matOutputs.forEach((matOutput: any, matIndex: number) => {
            if (matOutput.material && matOutput.material.trim() !== "" && matOutput.final) {
              console.log(`Creating final output component for template: ${matOutput.material}`);

              // Create final output node
              const finalOutputNodeId = `final-output-${sourceNodeId}-${matOutput.material.toLowerCase().replace(/\s+/g, '-')}`;

              // Calculate position using template layout constants
              const sourceNode = nodesList.find(n => n.id === sourceNodeId);
              let positionX = 150; // START_X from constants
              let positionY = 620; // ROW3_Y equivalent

              if (sourceNode) {
                // Position final output below the source node
                positionX = sourceNode.position.x;
                positionY = sourceNode.position.y + 250; // ROW_GAP from constants
              }

              const finalOutputNode = {
                id: finalOutputNodeId,
                type: 'finalOutput',
                data: { label: matOutput.material },
                position: { x: positionX, y: positionY },
                style: {},
              };

              // Add the final output node
              setNodes(nds => [...nds.filter(n => n.id !== finalOutputNodeId), finalOutputNode]);

              // Create edge from source to final output
              const edgeId = `final-output-edge-${sourceNodeId}-${finalOutputNodeId}`;
              const finalOutputEdge = {
                id: edgeId,
                source: sourceNodeId,
                sourceHandle: 'bottom-source',
                target: finalOutputNodeId,
                targetHandle: 'top-target',
                label: matOutput.material,
                data: {
                  type: 'final-output',
                  material: matOutput.material,
                  technology: output.outputTechnology || 'Boiler'
                },
                style: { stroke: '#f59e0b', strokeWidth: 3, opacity: 0.9 },
                labelStyle: {
                  fill: '#787882',
                  fontWeight: 400,
                  fontSize: 13.5,
                  userSelect: "none",
                  background: "rgba(255,255,255,0.7)",
                  padding: "2px 5px",
                  borderRadius: "3px",
                  border: "1px solid #ded3fd",
                },
                markerEnd: { type: MarkerType.ArrowClosed, color: "#f59e0b" },
                type: 'smoothstep'
              };

              setEdges(eds => [...eds.filter(e => e.id !== edgeId), finalOutputEdge]);
            }
          });
        }

        // Handle energy outputs marked as final
        if (output.energyOutputs && Array.isArray(output.energyOutputs)) {
          output.energyOutputs.forEach((energyOutput: any, energyIndex: number) => {
            if (energyOutput.energy && energyOutput.energy.trim() !== "" && energyOutput.final) {
              console.log(`Creating final output component for template: ${energyOutput.energy}`);

              // Create final output node
              const finalOutputNodeId = `final-output-${sourceNodeId}-${energyOutput.energy.toLowerCase().replace(/\s+/g, '-')}`;

              // Calculate position using template layout constants
              const sourceNode = nodesList.find(n => n.id === sourceNodeId);
              let positionX = 150; // START_X from constants
              let positionY = 620; // ROW3_Y equivalent

              if (sourceNode) {
                // Position final output below the source node
                positionX = sourceNode.position.x;
                positionY = sourceNode.position.y + 250; // ROW_GAP from constants
              }

              const finalOutputNode = {
                id: finalOutputNodeId,
                type: 'finalOutput',
                data: { label: energyOutput.energy },
                position: { x: positionX, y: positionY },
                style: {},
              };

              // Add the final output node
              setNodes(nds => [...nds.filter(n => n.id !== finalOutputNodeId), finalOutputNode]);

              // Create edge from source to final output
              const edgeId = `final-output-edge-${sourceNodeId}-${finalOutputNodeId}`;
              const finalOutputEdge = {
                id: edgeId,
                source: sourceNodeId,
                sourceHandle: 'bottom-source',
                target: finalOutputNodeId,
                targetHandle: 'top-target',
                label: energyOutput.energy,
                data: {
                  type: 'final-output',
                  energy: energyOutput.energy,
                  technology: output.outputTechnology || 'Boiler'
                },
                style: { stroke: '#f59e0b', strokeWidth: 3, opacity: 0.9 },
                labelStyle: {
                  fill: '#787882',
                  fontWeight: 400,
                  fontSize: 13.5,
                  userSelect: "none",
                  background: "rgba(255,255,255,0.7)",
                  padding: "2px 5px",
                  borderRadius: "3px",
                  border: "1px solid #ded3fd",
                },
                markerEnd: { type: MarkerType.ArrowClosed, color: "#f59e0b" },
                type: 'smoothstep'
              };

              setEdges(eds => [...eds.filter(e => e.id !== edgeId), finalOutputEdge]);
            }
          });
        }
      });
    }
  }

  // Smart positioning function to find available space around a source node
  function findAvailablePosition(sourceNode: any, preferredDirection: 'below' | 'right' | 'left' | 'above', existingNodes: any[]) {
    const NODE_WIDTH = 200;
    const NODE_HEIGHT = 100;
    const MIN_SPACING = 50;

    const baseOffsets = {
      below: { x: 0, y: NODE_HEIGHT + MIN_SPACING },
      right: { x: NODE_WIDTH + MIN_SPACING, y: 0 },
      left: { x: -(NODE_WIDTH + MIN_SPACING), y: 0 },
      above: { x: 0, y: -(NODE_HEIGHT + MIN_SPACING) }
    };

    const baseOffset = baseOffsets[preferredDirection];
    let positionX = sourceNode.position.x + baseOffset.x;
    let positionY = sourceNode.position.y + baseOffset.y;

    // Check if this position overlaps with existing nodes
    let attempts = 0;
    const maxAttempts = 10;

    while (attempts < maxAttempts) {
      const overlapping = existingNodes.some(node => {
        const distance = Math.sqrt(
          Math.pow(node.position.x - positionX, 2) +
          Math.pow(node.position.y - positionY, 2)
        );
        return distance < (NODE_WIDTH + MIN_SPACING);
      });

      if (!overlapping) {
        return { x: positionX, y: positionY };
      }

      // Try different positions based on direction
      attempts++;
      switch (preferredDirection) {
        case 'below':
          positionY += NODE_HEIGHT + MIN_SPACING;
          break;
        case 'right':
          positionX += NODE_WIDTH + MIN_SPACING;
          break;
        case 'left':
          positionX -= NODE_WIDTH + MIN_SPACING;
          break;
        case 'above':
          positionY -= NODE_HEIGHT + MIN_SPACING;
          break;
      }
    }

    // If we can't find a good position, use the last calculated position
    return { x: positionX, y: positionY };
  }

  // Function to create final output components for outputs marked as final
  function createFinalOutputComponents(nodeFormData: any, sourceNodeId: string) {
    console.log("=== CREATE FINAL OUTPUT COMPONENTS START ===");
    console.log("Creating final output components for node:", sourceNodeId);
    console.log("Node form data:", JSON.stringify(nodeFormData, null, 2));
    console.log("Outputs array:", nodeFormData.outputs);

    if (nodeFormData.outputs && Array.isArray(nodeFormData.outputs)) {
      nodeFormData.outputs.forEach((output: any, outputIndex: number) => {
        console.log(`Processing output ${outputIndex}:`, output);

        // Handle material outputs marked as final
        if (output.matOutputs && Array.isArray(output.matOutputs)) {
          output.matOutputs.forEach((matOutput: any, matIndex: number) => {
            if (matOutput.material && matOutput.material.trim() !== "" && matOutput.final) {
              console.log(`Creating final output component: ${matOutput.material}`);

              // Create final output node
              const finalOutputNodeId = `final-output-${sourceNodeId}-${matOutput.material.toLowerCase().replace(/\s+/g, '-')}`;

              // Use smart positioning to avoid overlapping
              const sourceNode = nodes.find(n => n.id === sourceNodeId);
              let positionX = 150; // Default fallback
              let positionY = 620; // Default fallback

              if (sourceNode) {
                // Use smart positioning to find available space below the source node
                const position = findAvailablePosition(sourceNode, 'below', nodes);
                positionX = position.x;
                positionY = position.y;
              }

              const finalOutputNode = {
                id: finalOutputNodeId,
                type: 'finalOutput',
                data: { label: matOutput.material },
                position: { x: positionX, y: positionY },
                style: {},
              };

              // Add the final output node
              setNodes(nds => [...nds.filter(n => n.id !== finalOutputNodeId), finalOutputNode]);

              // Create edge from source to final output
              const edgeId = `final-output-edge-${sourceNodeId}-${finalOutputNodeId}`;
              const finalOutputEdge = {
                id: edgeId,
                source: sourceNodeId,
                sourceHandle: 'bottom-source', // Connect from bottom of source node
                target: finalOutputNodeId,
                targetHandle: 'top-target', // Connect to top of final output node
                label: matOutput.material,
                data: {
                  type: 'final-output',
                  material: matOutput.material,
                  technology: output.outputTechnology || 'Boiler'
                },
                style: { stroke: '#f59e0b', strokeWidth: 3, opacity: 0.9 },
                labelStyle: {
                  fill: '#787882',
                  fontWeight: 400,
                  fontSize: 13.5,
                  userSelect: "none",
                  background: "rgba(255,255,255,0.7)",
                  padding: "2px 5px",
                  borderRadius: "3px",
                  border: "1px solid #ded3fd",
                },
                markerEnd: { type: MarkerType.ArrowClosed, color: "#f59e0b" },
                type: 'smoothstep'
              };

              setEdges(eds => [...eds.filter(e => e.id !== edgeId), finalOutputEdge]);
            }
          });
        }

        // Handle energy outputs marked as final
        if (output.energyOutputs && Array.isArray(output.energyOutputs)) {
          console.log(`Processing ${output.energyOutputs.length} energy outputs for output ${outputIndex}`);
          output.energyOutputs.forEach((energyOutput: any, energyIndex: number) => {
            console.log(`Energy output ${energyIndex}:`, energyOutput);
            if (energyOutput.energy && energyOutput.energy.trim() !== "" && energyOutput.final) {
              console.log(`Creating final output component: ${energyOutput.energy}`);

              // Create final output node
              const finalOutputNodeId = `final-output-${sourceNodeId}-${energyOutput.energy.toLowerCase().replace(/\s+/g, '-')}`;

              // Use smart positioning to avoid overlapping
              const sourceNode = nodes.find(n => n.id === sourceNodeId);
              let positionX = 600; // Default fallback
              let positionY = 300; // Default fallback

              if (sourceNode) {
                // Use smart positioning to find available space to the right of the source node
                const position = findAvailablePosition(sourceNode, 'right', nodes);
                positionX = position.x;
                positionY = position.y;
              }

              const finalOutputNode = {
                id: finalOutputNodeId,
                type: 'finalOutput',
                data: { label: energyOutput.energy },
                position: { x: positionX, y: positionY },
                style: {},
              };

              // Add the final output node
              setNodes(nds => [...nds.filter(n => n.id !== finalOutputNodeId), finalOutputNode]);

              // Create edge from source to final output
              const edgeId = `final-output-edge-${sourceNodeId}-${finalOutputNodeId}`;
              const finalOutputEdge = {
                id: edgeId,
                source: sourceNodeId,
                sourceHandle: 'bottom-source', // Connect from bottom of source node
                target: finalOutputNodeId,
                targetHandle: 'top-target', // Connect to top of final output node
                label: energyOutput.energy,
                data: {
                  type: 'final-output',
                  energy: energyOutput.energy,
                  technology: output.outputTechnology || 'Boiler'
                },
                style: { stroke: '#f59e0b', strokeWidth: 3, opacity: 0.9 },
                labelStyle: {
                  fill: '#787882',
                  fontWeight: 400,
                  fontSize: 13.5,
                  userSelect: "none",
                  background: "rgba(255,255,255,0.7)",
                  padding: "2px 5px",
                  borderRadius: "3px",
                  border: "1px solid #ded3fd",
                },
                markerEnd: { type: MarkerType.ArrowClosed, color: "#f59e0b" },
                type: 'smoothstep'
              };

              setEdges(eds => [...eds.filter(e => e.id !== edgeId), finalOutputEdge]);
            }
          });
        }
      });
    }
    console.log("=== CREATE FINAL OUTPUT COMPONENTS END ===");
  }

  // Enhanced subflow creation functionality
  const handleFlowAction = (type: FlowModeType, node) => {
    if (!node) return;
    
    if (isSubflowMode) {
      // If already in subflow mode, save current subflow first
      handleSaveFlow();
    } else {
      // Save the current state before entering subflow mode
      setOriginalNodes([...nodes]);
      setOriginalEdges([...edges]);
      
      // Enter subflow mode
      setIsSubflowMode(true);
      setActiveNodeId(node.id);
      setSubflowType(type as "subflow" | "byproduct");
      
      // Update scenario name to reflect subflow relationship
      const newScenarioName = `${type === "subflow" ? "Subflow" : "Byproduct"} of ${node.data.label}`;
      setScenarioName(newScenarioName);
      
      // Keep only the selected node and remove all other nodes
      const selectedNode = nodes.find(n => n.id === node.id);
      if (selectedNode) {
        // Clone the node to avoid reference issues
        const clonedNode = {
          ...selectedNode,
          position: { x: 420, y: 200 }, // Center the node in the view
        };
        
        // Filter out all edges related to this node
        setNodes([clonedNode]);
        setEdges([]);
      }
      
      toast({ 
        title: `${type === "subflow" ? "Subflow" : "Byproduct Flow"} Mode`, 
        description: `Now working on a ${type === "subflow" ? "subflow" : "byproduct flow"} for ${node.data.label}. All other nodes have been hidden.` 
      });
    }
    
    setSelectedNodeId(null);
  };

  const saveCurrentFlow = () => {
    handleSaveFlow();
  };

  const onNodeSelect = useCallback((event, node) => {
    event.preventDefault();
    event.stopPropagation();
    setSelectedNodeId(node.id);
  }, []);

  const handleFlowPaneClick = () => {
    setSelectedNodeId(null);
  };

  React.useEffect(() => {
    if (window.localStorage.getItem('flowEntry')) {
      try {
        const entry = JSON.parse(window.localStorage.getItem('flowEntry'));
        setNodes(entry.nodes || []);
        setEdges(entry.edges || []);
      } catch (e) {
      }
      window.localStorage.removeItem('flowEntry');
      setFlowMode('scratch');
      setShowModeDialog(false);
    }
  }, []);

  const selectedNode = selectedNodeId ? nodes.find((n) => n.id === selectedNodeId) : null;

  // Modify nodes when in subflow mode - but now we're actually only showing the active node
  const displayNodes = React.useMemo(() => {
    // Since we're now removing other nodes when entering subflow mode,
    // we just return the nodes as is
    return nodes;
  }, [nodes]);

  const [sidebarVisible, setSidebarVisible] = useState(true);

  const handleScenarioNameChange = (newName: string) => {
    // If the new name is empty, we'll keep it empty and the default text will be shown from the component
    setScenarioName(newName.trim());
  };

  // Handler for running scenario optimization (legacy - now handled by OptimizerSidebar)
  const handleRunScenarioLegacy = () => {
    if (isScenarioMode) {
      setScenarioComparisonOpen(true);
    }
  };

  // Handler for viewing results board (legacy - now handled by OptimizerSidebar)
  const handleViewResultsLegacy = () => {
    setResultsBoardOpen(true);
  };

  // Handle technology selection for a node - we'll keep this for compatibility
  const handleTechnologySelect = useCallback((nodeId: string, technologyId: string) => {
    setNodes((nds) =>
      nds.map((node) => {
        if (node.id === nodeId) {
          // Update the node data with the selected technology
          return {
            ...node,
            data: {
              ...node.data,
              technology: technologyId,
            },
          };
        }
        return node;
      })
    );
    
    // Show a toast notification
    toast({ 
      title: 'Technology Applied', 
      description: `Technology has been applied to the node.`
    });
  }, [setNodes, toast]);

  // New effect to listen for the custom event to add a new activity node
  useEffect(() => {
    const handleAddNewActivityNode = (event: CustomEvent) => {
      const { node, sourceNodeId } = event.detail;
      
      // Find the source node to position the new node relative to it
      const sourceNode = nodes.find(n => n.id === sourceNodeId) || 
                         nodes.find(n => n.id.includes(sourceNodeId));
      
      // Calculate position for the new node - place it to the right of the source node
      let positionX = 300;
      let positionY = 200;
      
      if (sourceNode) {
        positionX = sourceNode.position.x + 250; // Place to the right
        positionY = sourceNode.position.y;       // Same vertical position
      }
      
      // Create the new node with calculated position
      const newNode = {
        ...node,
        position: { x: positionX, y: positionY },
        style: { ...nodeDefaultStyle },
      };
      
      // Add the new node to the diagram
      setNodes(nds => [...nds, newNode]);
      
      // If we're in inventory mode, update the inventory nodes too
      if (!isScenarioMode && !isSubflowMode) {
        setInventoryNodes(invNodes => [...invNodes, newNode]);
      }
      
      toast({
        title: "New Activity Added",
        description: `Added "${newNode.data.label}" to the diagram`
      });
    };
    
    // Add event listener
    document.addEventListener('add-new-activity-node', handleAddNewActivityNode as EventListener);
    
    // Remove event listener on cleanup
    return () => {
      document.removeEventListener('add-new-activity-node', handleAddNewActivityNode as EventListener);
    };
  }, [nodes, isScenarioMode, isSubflowMode, toast]);
  
  // Add the missing delete handler functions
  const handleDeleteNode = (nodeId: string) => {
    setNodeToDelete(nodeId);
    setShowDeleteDialog(true);
  };

  const cancelDeleteNode = () => {
    setShowDeleteDialog(false);
    setNodeToDelete(null);
  };

  const confirmDeleteNode = () => {
    if (!nodeToDelete) return;

    // Remove all edges connected to this node (both incoming and outgoing)
    setEdges((eds) => eds.filter((edge) => 
      edge.source !== nodeToDelete && edge.target !== nodeToDelete
    ));

    // Remove the node itself
    setNodes((nds) => nds.filter((node) => node.id !== nodeToDelete));

    // Also update inventory nodes if not in scenario mode or subflow mode
    if (!isScenarioMode && !isSubflowMode) {
      setInventoryNodes((invNodes) => invNodes.filter((node) => node.id !== nodeToDelete));
      setInventoryEdges((invEdges) => invEdges.filter((edge) => 
        edge.source !== nodeToDelete && edge.target !== nodeToDelete
      ));
    }

    // Clear selection
    setSelectedNodeId(null);

    // Close dialog and clear state
    setShowDeleteDialog(false);
    setNodeToDelete(null);

    toast({
      title: "Node Deleted",
      description: "The node and all its connections have been removed."
    });
  };

  // Handler for marketplace navigation
  const handleMarketplaceClick = useCallback((nodeId: string) => {
    const node = nodes.find(n => n.id === nodeId);
    if (node?.data?.label) {
      const activitySlug = getActivitySlug(node.data.label);
      navigate(`/marketplace/${activitySlug}`);
    } else {
      toast({
        title: "Error",
        description: "Could not find activity details for this node.",
        variant: "destructive",
      });
    }
  }, [nodes, navigate, toast]);

  if (showModeDialog) {
  return (
      <FlowModeDialog open={showModeDialog} onSelect={handleModeSelect} />
    );
  }

  // Define flowTypeLabel before using it
  let flowTypeLabel = isScenarioMode ? "Scenario" : "Inventory";
  if (flowTypeFromState === "subflow") {
    flowTypeLabel = "Subflow";
  } else if (flowTypeFromState === "byproduct") {
    flowTypeLabel = "By-product Flow";
  } else if (isSubflowMode) {
    flowTypeLabel = subflowType === "subflow" ? "Subflow Mode" : "By-product Flow Mode";
  }

  // Removed the special case for scratch mode with no nodes
  // Now build from scratch uses the same layout as default flow

  return (
    <div className="min-h-screen flex w-full">
      {/* OptimizerSidebar for optimizer context */}
      <OptimizerSidebar
        isScenarioMode={isScenarioMode}
        hasConstraints={hasConstraints}
        hasRunScenario={hasRunScenario}
        isRunningScenario={isRunningScenario}
        scenarioName={scenarioName}
        setScenarioName={setScenarioName}
        editingScenarioName={editingScenarioName}
        setEditingScenarioName={setEditingScenarioName}
        handleScenarioNameChange={handleScenarioNameChange}
        savedFlows={savedFlows}
        flowDiagrams={flowDiagrams}
        isLoadingFlowDiagrams={isLoadingFlowDiagrams}
        onSaveInventory={handleSaveFlow}
        onLoadInventory={handleLoadFlow}
        onCreateScenario={handleCreateScenario}
        onAddConstraints={handleAddConstraints}
        onRunScenario={handleRunScenario}
        onViewResults={handleViewResults}
        onSaveScenario={handleSaveFlow}
        onLoadScenario={handleLoadFlow}
        onSaveFlowDiagram={saveFlowToDatabase}
        onLoadFlowDiagram={loadFlowDiagram}
        onRefreshFlowDiagrams={refreshFlowDiagrams}
      />

      <div
        className={`flex-1 flex flex-col ${theme === 'dark'
          ? 'bg-recrea-dark text-white'
          : 'bg-white text-recrea-dark'
        }`}
        style={{ fontFamily: 'Inter, sans-serif' }}
        onClick={handleFlowPaneClick}
      >
      <IndustryFlowHeader
        sectorName={sectorName}
        flowTypeLabel={flowTypeLabel}
        scenarioName={scenarioName}
        isScenarioMode={isScenarioMode}
        onExitScenario={exitScenarioMode}
      />

      <main className={`relative flex-1 w-full ${isScenarioMode && sidebarVisible ? 'pr-80' : ''}`}>
        <div className="absolute top-8 left-8 z-10 flex flex-col gap-3">
          <button
            className="bg-white shadow border border-[#ded3fd] w-12 h-12 rounded-full flex items-center justify-center hover:bg-[#f5f2fc]"
            onClick={handlePlusClick}
            aria-label="Add node"
            style={{ fontSize: 26 }}
          >
            <Plus color="#7E69AB" size={32} />
          </button>
          
          <button
            className="bg-[#FFEDD5] shadow border border-[#FB923C] w-12 h-12 rounded-full flex items-center justify-center hover:bg-[#FFECD1]"
            onClick={handleAddFinalOutputNode}
            aria-label="Add final output node"
            title="Add Final Output Node"
          >
            <CircleX color="#7C2D12" size={24} />
          </button>



          {/* Delete Node Button - only show when a node is selected */}
          {selectedNodeId && (
            <button
              className="bg-[#FEE2E2] shadow border border-[#EF4444] w-12 h-12 rounded-full flex items-center justify-center hover:bg-[#FECACA]"
              onClick={() => handleDeleteNode(selectedNodeId)}
              aria-label="Delete selected node"
              title="Delete Selected Node"
            >
              <Trash color="#DC2626" size={20} />
            </button>
          )}
        </div>
        
        <div className="w-full h-[660px] mx-auto" style={{maxWidth:1040}}>
        <ReactFlow
            ref={reactFlowRef}
            nodes={displayNodes}
          edges={edges}
            nodeTypes={nodeTypes}

          onNodesChange={onNodesChange}
          onEdgesChange={onEdgesChange}
          onConnect={onConnect}
            onConnectStart={onConnectStart}
            onConnectEnd={onConnectEnd}
            onNodeDoubleClick={handleNodeDoubleClick}
            onEdgeDoubleClick={handleEdgeDoubleClick}
          fitView
            panOnScroll
            zoomOnScroll
            selectionOnDrag
            className={theme === 'dark' ? 'dark-flow' : 'light-flow'}
            style={{
              background: 'repeating-dotted'
            }}
            onNodeClick={(event, node) => {
              event.stopPropagation();
              onNodeSelect(event, node);
            }}
            onPaneClick={handleFlowPaneClick}
          >
            <Background
              gap={28}
              size={1}
              color={'#ded3fd'}
            />

            <Controls showInteractive={false} />
            <MiniMap nodeStrokeColor="#7E69AB" nodeColor="#E5DEFF" />
        </ReactFlow>

        {/* Empty state overlay for build from scratch mode */}
        {flowMode === 'scratch' && nodes.length === 0 && (
          <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
            <button
              onClick={handleAddFirstNode}
              className="flex flex-col items-center gap-1 border-2 border-gray-300 rounded-xl px-6 py-8 bg-white shadow hover:shadow-lg transition-shadow pointer-events-auto"
            >
              <Plus size={32} />
              <div className="font-medium mt-1 text-lg">Add an activity</div>
            </button>
          </div>
        )}

          <div className="absolute left-8 bottom-8 z-20">
            <div className="relative">
              <Button
                variant="secondary"
                className="rounded-full px-6 py-3 flex items-center gap-2 bg-[#EFEAFB] text-[#7E69AB] font-semibold text-base"
                onClick={() => setPopoverOpen((v) => !v)}
                style={{
                  boxShadow: '0 3px 12px #eae3ff84',
                  border: '1px solid #ded3fd',
                }}
              >
                {isSubflowMode ? (subflowType === "subflow" ? "Subflow Mode" : "By-product Flow Mode") : "Inventory Mode"}
                <Plus size={18} />
              </Button>
              
              {/* Return buttons for different modes */}
              {isSubflowMode && (
                <Button
                  variant="outline"
                  className="ml-2 rounded-full px-6 py-3 flex items-center gap-2 bg-white text-[#7E69AB] font-semibold text-base"
                  onClick={exitSubflowMode}
                  style={{
                    boxShadow: '0 3px 12px #eae3ff84',
                    border: '1px solid #ded3fd',
                  }}
                >
                  Return to {isScenarioMode ? "Scenario" : "Inventory"}
                </Button>
              )}
              
              {isScenarioMode && !isSubflowMode && (
                <Button
                  variant="outline"
                  className="ml-2 rounded-full px-6 py-3 flex items-center gap-2 bg-white text-[#7E69AB] font-semibold text-base"
                  onClick={exitScenarioMode}
                  style={{
                    boxShadow: '0 3px 12px #eae3ff84',
                    border: '1px solid #ded3fd',
                  }}
                >
                  Return to Inventory
                </Button>
              )}
              
              {popoverOpen && (
                <div
                  className="mt-2 absolute left-0 rounded-lg shadow-xl bg-white font-medium min-w-[160px] border border-[#ded3fd] py-2 text-base"
                  style={{ zIndex: 20 }}
                >
                  {flowTypes.map((ft) => (
                    <div
                      key={ft.id}
                      className={`flex items-center gap-2 px-4 py-2 cursor-pointer hover:bg-[#EFEAFB]`}
                      onClick={() => setPopoverOpen(false)}
                    >
                      <div className={`w-3 h-3 rounded-full mr-2 ${ft.color}`}></div>
                      {ft.label}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      {selectedNode && selectedNode.type !== 'finalOutput' && (
        <IndustryFlowNodeActions
          selectedNode={selectedNode}
          handleFlowAction={handleFlowAction}
            handleRename={() => setEditNode(selectedNode ? { id: selectedNode.id, label: selectedNode.data.label } : null)}
            handleTechnologySelect={handleTechnologySelect}
            onOpenConnectionForm={handleOpenConnectionForm}
            onMarketplaceClick={handleMarketplaceClick}
          />
        )}
      </main>



      {/* Add Constraints Modal */}
      <AddConstraintsModal
        open={showConstraintsModal}
        onClose={() => setShowConstraintsModal(false)}
        onSave={handleConstraintsAdded}
        scenarioId={scenarioName || 'current-scenario'}
        scenarioName={scenarioName}
        flowType="SCENARIO_MAIN"
        industryId={industryId}
      />

      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Trash size={20} className="text-red-600" />
              Delete Node
            </DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this node and all associated connections? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="gap-2">
            <Button variant="outline" onClick={cancelDeleteNode}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={confirmDeleteNode} className="flex items-center gap-2">
              <Trash size={16} />
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

        <NodeCreationDialog
        open={showNodeCreationDialog}
        onClose={() => setShowNodeCreationDialog(false)}
        onNodeCreate={handleNodeCreate}
        industryId={industryId || 'default'}
        title="Create New Node"
        description="Select an activity for the new node. You can choose from existing activities or create a new one."
        existingNodeNames={nodes.filter(node => node.data?.label).map(node => node.data.label)}
      />

      <EditNodeDialog
        editNode={editNode}
        setEditNode={setEditNode}
        handleUpdateNodeName={handleUpdateNodeName}
        industryId={industryId}
        existingNodeNames={nodes.filter(node => node.data?.label).map(node => node.data.label)}
      />

      <EditEdgeDialog
        editEdge={editEdge}
        setEditEdge={setEditEdge}
        handleUpdateEdgeName={handleUpdateEdgeName}
      />

      <ConnectionFormDialog
        open={showConnectionDialog}
        onClose={handleConnectionModalClosed}
        onComplete={handleConnectionComplete}
        autoFillInputs={autoFillInputs || []}
        sourceNode={connectionSourceNode}
        targetNode={targetNodeId}
        availableNodes={availableTargetNodes}
        incomingConnectionData={incomingConnectionData}
        existingFormData={connectionSourceNode ? nodeFormData[connectionSourceNode.id] : undefined}
        industryId={industryId}
        // New dual tab props for scenario mode
        isScenarioMode={isScenarioMode}
        scenarioTabMode={scenarioTabMode}
        onScenarioTabChange={handleScenarioTabSwitch}
        baseScenarioData={connectionSourceNode ? getDisplayDataForTab(connectionSourceNode.id, 'base') : undefined}
        baseScenarioName={baseScenarioFlowData?.name || baseScenarioName || 'Base Scenario'}
        currentScenarioName={scenarioName || 'Current Scenario'}
        isNewNode={connectionSourceNode ? !nodeFormData[connectionSourceNode.id] && !baseScenarioData[connectionSourceNode.id] : false}
      />

      {/* Dual-Tab Node Editor */}
      <DualTabNodeEditor
        open={dualTabEditorOpen}
        onClose={() => setDualTabEditorOpen(false)}
        onSave={(formData) => {
          // Handle saving the scenario node data
          if (dualTabNodeId) {
            console.log('Saving scenario node data for:', dualTabNodeId, formData);

            // Update the node form data with scenario changes
            setNodeFormData(prev => ({
              ...prev,
              [dualTabNodeId]: formData
            }));

            // Update the visual diagram to reflect changes
            handleConnectionComplete(formData);
          }
          setDualTabEditorOpen(false);
        }}
        nodeId={dualTabNodeId || ''}
        baseScenarioData={(() => {
          if (!dualTabNodeId) return undefined;
          console.log('=== BASE SCENARIO DATA DEBUG ===');
          console.log('dualTabNodeId:', dualTabNodeId);
          console.log('baseScenarioData state:', baseScenarioData);
          console.log('baseScenarioData keys:', Object.keys(baseScenarioData));
          const baseData = baseScenarioData[dualTabNodeId];
          console.log('baseData for node', dualTabNodeId, ':', baseData);
          console.log('=== END BASE SCENARIO DATA DEBUG ===');
          return baseData;
        })()}
        currentScenarioData={(() => {
          if (!dualTabNodeId) return undefined;
          const currentData = nodeFormData[dualTabNodeId];
          console.log('DualTabNodeEditor currentScenarioData for node', dualTabNodeId, ':', currentData);
          return currentData;
        })()}
        isNewNode={dualTabNodeId ? !nodeFormData[dualTabNodeId] && !baseScenarioData[dualTabNodeId] : false}
        activityName={dualTabNodeId ? nodes.find(n => n.id === dualTabNodeId)?.data?.label || '' : ''}
        technologyName={dualTabNodeId ? nodes.find(n => n.id === dualTabNodeId)?.data?.technology || '' : ''}
        baseScenarioName={baseScenarioName || 'Base Scenario'}
        currentScenarioName={scenarioName || 'Current Scenario'}
        availableNodes={nodes}
      />

      {/* Scenario Comparison Dialog */}
      <ScenarioComparison
        open={scenarioComparisonOpen}
        onClose={() => {
          setScenarioComparisonOpen(false);
          setIsRunningScenario(false);
        }}
        onComplete={(result) => {
          console.log('Optimization completed:', result);
          setScenarioComparisonOpen(false);
          setIsRunningScenario(false);
          setHasRunScenario(true);
          // Don't auto-open results board, let user click "View Results"
        }}
        baseScenarioName="Base Scenario"
        currentScenarioName={scenarioName || 'Current Scenario'}
      />

      {/* Results Board Dialog */}
      {resultsBoardOpen && (
        <Dialog open={resultsBoardOpen} onOpenChange={setResultsBoardOpen}>
          <DialogContent className="max-w-7xl max-h-[90vh] overflow-hidden">
            <DialogHeader>
              <DialogTitle>Optimization Results</DialogTitle>
            </DialogHeader>
            <div className="h-[calc(90vh-120px)]">
              <ResultsBoard
                onSelectResult={(result) => console.log('Selected result:', result)}
                onNewOptimization={() => {
                  setResultsBoardOpen(false);
                  setScenarioComparisonOpen(true);
                }}
              />
            </div>
          </DialogContent>
        </Dialog>
      )}

      {/* Overwrite Confirmation Dialog */}
      <OverwriteConfirmDialog
        open={showOverwriteDialog}
        onOpenChange={setShowOverwriteDialog}
        flowName={scenarioName}
        flowType={isScenarioMode ? 'scenario' : 'inventory'}
        onConfirm={handleOverwriteConfirm}
        onCancel={handleOverwriteCancel}
      />

      </div>
    </div>
  );
};

export default IndustryFlow;
